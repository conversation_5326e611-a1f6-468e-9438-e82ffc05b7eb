const Service = require('egg').Service
const { getResponseBody } = require('../extend/utils')
const { EXPORT_COMTYPE } = require('../extend/constant')
const {
  getPrevComProjection,
  componentListToObj,
  getCombinationScreenModel
} = require('../utils/preview')

class PreviewService extends Service {
  /**
   * 通过大屏id获取组件列表数据
   * @param {array} screenIds
   * @returns
   */
  async getComponentListByScreenIds(screenIds = []) {
    const { ctx } = this
    this.ctx.logger.info('componentstart:', new Date().getTime())
    return await ctx.service.component
      .find(
        {
          screenId: {
            $in: screenIds
          }
        },
        {
          ...getPrevComProjection(),
          events: 0
        }
      )
      .then(res => {
        this.ctx.logger.info('componentend:', new Date().getTime())
        return res
      })
  }

  /**
   * 通过大屏id获取过滤恶器列表数据
   * @param {array} screenIds
   * @returns
   */
  async getFilterListByScreenIds(screenIds = []) {
    const { ctx } = this
    this.ctx.logger.info('filterDataStart:', new Date().getTime())
    return await ctx.service.filter
      .find({
        screenId: {
          $in: screenIds
        }
      })
      .then(res => {
        this.ctx.logger.info('filterDataEnd:', new Date().getTime())
        return res
      })
  }

  /**
   * 获取大屏、面板组合后的数据，包括组件数据、过滤器数据
   * @param {*} screenIds
   * @returns
   */
  async getCombinationScreens(screenIds = []) {
    const { ctx, config } = this
    const voiceConfig = config.voiceConfig || {
      voiceWeak: false,
      weakWord: '你好伏羲'
    }
    const previewConfig = config.previewConfig || {
      // 发布页面设置
      blankShow: false // 是否开启空白页面配置
    }
    const screenList = await ctx.service.screen.find({
      id: {
        $in: screenIds
      }
    })

    const componentList = await this.getComponentListByScreenIds(screenIds)
    const filterList = await this.getFilterListByScreenIds(screenIds)

    // 保存结果
    const screenListRes = []

    for (let index = 0; index < screenList.length; index++) {
      const screen = screenList[index]
      screenList[index] = screenList[index].toObject()

      const screenInfo = screen

      // 组件数据筛选
      const currScreenComps = componentList.filter(comp => {
        return comp.screenId === screen.id
      })

      // 过滤器数据筛选
      const currScreenFilters = filterList.filter(filter => {
        return filter.screenId === screen.id
      })

      // 是否是动态面板类型
      const isDynamicScreen = screenInfo.isDynamicScreen || false

      // 组件列表转换为对象
      const components = componentListToObj(currScreenComps)

      screenListRes.push(
        getCombinationScreenModel(screenInfo, {
          filter: currScreenFilters,
          components: components,
          voiceConfig,
          previewConfig
        })
      )
    }

    return screenListRes
  }
  /**
   * 获取发布页大屏数据或者缓存
   * @param {number} screenId 大屏ID
   * @param {string} shareToken shareToken
   * @param {boolean} updateCatch 是否更新不走缓存
   * @returns
   */
  async getPreviewScreenOrCache({ screenId, shareToken, updateCatch = false }) {
    const { ctx, config } = this
    const voiceConfig = config.voiceConfig || {
      voiceWeak: false,
      weakWord: '你好伏羲'
    }
    const previewConfig = config.previewConfig || {
      // 发布页面设置
      blankShow: false // 是否开启空白页面配置
    }
    const id = screenId
    const key = `preview_screen_${id || shareToken}`
    const apistart = new Date().getTime()

    let value = await ctx.service.cacheservice.get(key)
    try {
      if (value && typeof value === 'string') {
        value = JSON.parse(value)
      }
    } catch (error) {
      this.ctx.logger.error(error)
    }

    // 走缓存的条件
    if (value && !updateCatch) {
      // ctx.body = getResponseBody(value)
      const apiend = new Date().getTime()
      this.ctx.logger.info(
        '预览screen接口缓存请求速度:',
        apiend - apistart,
        'hzms'
      )
      return getResponseBody(value)
    }
    this.ctx.logger.info('不走缓存', 'hzms')

    this.ctx.logger.info('不走缓存', 'fn:preview.screen', `key:${key}`)

    if (!id && !shareToken) {
      const msg = 'shareToken参数或id参数错误'
      // ctx.body = getResponseBody(null, false, msg, 400)
      this.ctx.logger.info(msg, `shareToken: ${shareToken}, id: ${id}`)
      return getResponseBody(null, false, msg, 400)
    }

    let screenShare

    if (shareToken) {
      screenShare = await ctx.service.screenshare.getScreenShareByShareToken({
        shareToken: shareToken
      })

      if (!screenShare) {
        const msg = '没有这个大屏，请检查分享链接是否已过期'
        // ctx.body = getResponseBody(null, false, msg, 400)
        this.ctx.logger.info(msg, `shareToken: ${shareToken}`)
        return getResponseBody(null, false, msg, 400)
      }
    }

    const screenInfo = await ctx.service.screen.findOne({
      id: Number(id || screenShare.screenId)
    })
    if (!screenInfo) {
      const msg = '没有这个大屏id'
      // ctx.body = getResponseBody(null, false, msg, 400)
      this.ctx.logger.info(msg, `id: ${Number(id || screenShare.screenId)}`)
      return getResponseBody(null, false, msg, 400)
    }

    // 是否是动态面板类型
    const isDynamicScreen = screenInfo.isDynamicScreen || false
    // 是否是指标卡
    const isIndicator = screenInfo.parentId == '1' ? true : false
    // screenSocket
    let screenSocket = {}
    // 字体列表
    let fonts = []

    /**
     * 获取Screensocket
     * @returns Promise
     */
    const getScreensocket = () => {
      return ctx.service.screensocket
        .getScreensocketByScreenId({
          screenId: screenInfo.id
        })
        .then(res => {
          screenSocket = res || {}
          return res
        })
    }

    /**
     * 获取字体列表数据
     * @returns Promise
     */
    const getFontList = () => {
      return ctx.service.font.find().then(res => {
        fonts = (res || []).map(item => {
          return {
            enable: item.enable,
            fontName: item.fontName,
            fontUrl: item.fontUrl,
            id: item.id,
            systemFont: item.systemFont
          }
        })
        return res
      })
    }
    let componentList = []
    // 过滤器数据
    let filterList = []

    // promiseList
    const promiseList = [
      this.getComponentListByScreenIds([screenInfo.id]),
      this.getFilterListByScreenIds([screenInfo.id])
    ]

    // 分享页数据
    if (!isDynamicScreen || isIndicator) {
      promiseList.push(getScreensocket(), getFontList())
    }

    try {
      await Promise.all(promiseList).then((resList = []) => {
        componentList = resList[0] || []
        filterList = resList[1] || []
      })

      // 组件列表转换为对象
      const components = componentListToObj(componentList)

      const res = getCombinationScreenModel(screenInfo, {
        components: components,
        filter: filterList,
        voiceConfig,
        previewConfig
      })

      if (!isDynamicScreen || isIndicator) {
        res.screenShare = screenShare
        res.screenSocket = screenSocket
        res.fonts = fonts
      }

      ctx.service.cacheservice.set(key, res, 15768000)

      const apiend = new Date().getTime()
      this.ctx.logger.info(
        '预览screen接口不缓存请求速度:',
        apiend - apistart,
        'hzms'
      )
      return getResponseBody(res)
    } catch (error) {
      // ctx.body = getResponseBody(null, false, '获取大屏失败', 400)
      this.ctx.logger.error('获取大屏失败', error)
      return getResponseBody(null, false, '获取大屏失败', 400)
    }
  }

  /**
   * 清除发布页缓存
   * @param {number} screenId 大屏ID
   * @returns
   */
  async clearCache({ screenId }) {
    const { ctx } = this
    try {
      const screenshareInfo = await ctx.service.screenshare.findOne({
        screenId
      })
      const comInfo = await ctx.service.component.find({
        screenId,
        comType: { $in: EXPORT_COMTYPE.COPYCOMTYPE }
      })
      const screenIds = []

      if (comInfo) {
        // 获取面板大屏id
        for (let index = 0; index < comInfo.length; index++) {
          const element = comInfo[index]
          if (element.config.screens && element.config.screens.length) {
            screenIds.push(...element.config.screens.map(item => item.id))
          }
        }
      }

      // 清除缓存
      const pipeline = this.app.redis.get('db0').pipeline()
      for (let k = 0; k < screenIds.length; k++) {
        const id = screenIds[k]
        pipeline.del(`preview_screen_${id}`)
      }
      pipeline.del(`preview_screen_${screenshareInfo.shareToken}`)
      pipeline.del(`preview_screen_${screenshareInfo.screenId}`)

      pipeline.del(`${screenshareInfo.screenId}_preview_panel_screens`)
      await pipeline.exec()

      return getResponseBody('', true, '缓存清除成功', 200)
    } catch (error) {
      this.ctx.logger.error('缓存清除失败', error)
      return getResponseBody(error, false, '缓存清除失败', 400)
    }
  }
}

module.exports = PreviewService

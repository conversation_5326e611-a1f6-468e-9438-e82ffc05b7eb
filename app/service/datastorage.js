const Service = require('egg').Service
const {
  getCommonProjection,
  initDrill,
  drilldownByPath,
  uuid
} = require('../extend/utils')
const dbconns = require('../helper/datasource')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES } = require('../extend/constant')
const redis = require('redis')
const { promisify } = require('util');
const path = require('path')
const fs = require('fs');
const XLSX = require('xlsx');
const mongoose = require('mongoose');
const moment = require('moment');
const schema = require('../extend/schema')
const message = require('../contract/message')
const { Parser } = require('node-sql-parser')
const parser = new Parser()

class DataStorageService extends Service {
  async findWithTimeout(promise, timeout) {
    return new Promise((resolve, reject) => {
      // 设置一个定时器，在指定的超时时间后拒绝 Promise
      const timer = setTimeout(() => {
        reject(new Error('查询超时'));
      }, timeout);
  
      // 执行传入的 Promise
      promise
        .then((result) => {
          clearTimeout(timer); // 清除定时器
          resolve(result); // 解析结果
        })
        .catch((err) => {
          clearTimeout(timer); // 清除定时器
          reject(err); // 拒绝错误
        });
    });
  }
  async find(filter, projection, options) {
    try {
      // 创建一个查询的 Promise
      const queryPromise = this.ctx.model.Datastorage.find(
        filter,
        getCommonProjection(projection),
        options
      ).exec();

      // 使用带超时功能的查询
      const res = await this.findWithTimeout(queryPromise, 20000); // 20秒超时
      return res;
    } catch (error) {
      this.ctx.logger.error(error);
      throw error;
    }
  }
  // async find(filter, projection, options) {
  //   const res = await this.ctx.model.Datastorage.find(
  //     filter,
  //     getCommonProjection(projection),
  //     options
  //   )
  //   return res
  // }
  async create(params) {
    if(params.type === 'excel') {
      params.isNewFlag = true
    }
    const res = await this.ctx.model.Datastorage.create(params)
    this.ctx.logger.info('原本的res,',res)
    if (params.type === 'excel') {
      const { config } = params;
      let { filePath, fieldsType } = config;
      filePath = filePath.replace('public/', '/')
      const excelFilePath = path.resolve(
        this.config.resourcePath,
        `./${filePath}`
      )
      const binaryData = await fs.readFileSync(excelFilePath, 'binary')
      const workbook = XLSX.read(binaryData, { type: 'binary', cellText:false, cellDates:true });
      const sheetList = workbook.SheetNames.map((sheetName) => {
        const sheetData = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName] ,{ raw: false, dateNF: 'yyyy-mm-dd' });
        let arr = sheetData
        console.log('sheetData222',JSON.stringify(arr));
        console.log('sheetData====',sheetData);
        const sheetId = new mongoose.Types.ObjectId();
        const sheetCollectionName = `Sheet_${sheetId}`;
        const sheetSchema = schema.getSheetSchema(sheetCollectionName)
        const SheetModel = mongoose.model(`Sheet_${sheetId}`, sheetSchema);
        const sheetDocument = new SheetModel({
          sheetId,
          sheetData
        });
        sheetDocument.save();
        const sheetFields = sheetData.length > 0 ? Object.keys(sheetData[0]).map(fieldName => ({
          fieldName,
          fieldType: fieldsType[fieldName] || 'string' // 使用 fieldsType 中的类型，默认 'string'
        })) : [];
      
        return {
          sheetId,
          sheetName,
          sheetFields,
          sheetCollection: sheetCollectionName
        };
      });
      let excelData = {
        excelId: uuid(),
        excelName: params.name,
        createTime: new Date(),
        sheetList,
        id: res.id // 和datastorage关联的数据
      }
      const excelRes = await this.ctx.model.Excel.create(excelData)
      this.ctx.logger.info('excelRes',excelRes);
    }
    return res
    
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Datastorage.insertMany(docs, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Datastorage.deleteOne(params)
    // 因为不传excel，通过id查找是否在excelBase里存在这个数据
    const targetExcel = await this.ctx.model.Excel.findOne(params)
    this.ctx.logger.info('删除的targetExcel', targetExcel)
    // 在baseExcel里存在，删除对应的sheet里的 再删除excelBase里的数据
    if (targetExcel) {
      targetExcel.sheetList.forEach(async (item) => {
        // 遍历sheetList的sheetCollcetion 然后删除对应的collection
        await this.app.mongoose.connection.dropCollection(item.sheetCollection);
        await this.ctx.model.Excel.deleteOne(params)
      })
    }
    return res
  }
  async update(filter, params, options) {
    console.log('filer',filter,'params', params, 'option',options);
    const res = await this.ctx.model.Datastorage.updateOne(
      filter,
      params,
      options
    )
    if (params.type == 'excel') {
      // 通过文件路径找到内容 拼装新的params 然后通过fiter搜索 替换进去 使用updateOne更新
      // sheet？ 删除原本的sheet 然后在根据创建步骤新建sheet
      // 那为什么不删除再重新新建呢？主要是名字就要改啊 那还不如全部都删了再新建
      // 删对应的sheet和excel表格里的内容
      const targetExcel = await this.ctx.model.Excel.findOne(filter)
      if(targetExcel){
        targetExcel.sheetList.forEach(async (item) => {
          await this.app.mongoose.connection.dropCollection(item.sheetCollection);
        })
        await this.ctx.model.Excel.deleteOne(filter)
      }
      // 新增
      const { config } = params;
      let { filePath, fieldsType } = config;
      filePath = filePath.replace('public/', '/')
      const excelFilePath = path.resolve(
        this.config.resourcePath,
        `./${filePath}`
      )
      const binaryData = await fs.readFileSync(excelFilePath, 'binary')
      const workbook = XLSX.read(binaryData, { type: 'binary', cellText:false, cellDates:true });
      const sheetList = workbook.SheetNames.map((sheetName) => {
        const sheetData = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName] ,{ raw: false, dateNF: 'yyyy-mm-dd' });
        let arr = sheetData
        console.log('sheetData222',JSON.stringify(arr));
        console.log('sheetData====',sheetData);
        const sheetId = new mongoose.Types.ObjectId();
        const sheetCollectionName = `Sheet_${sheetId}`;
        const sheetSchema = schema.getSheetSchema(sheetCollectionName)
        const SheetModel = mongoose.model(`Sheet_${sheetId}`, sheetSchema);
        const sheetDocument = new SheetModel({
          sheetId,
          sheetData
        });
        sheetDocument.save();
        const sheetFields = sheetData.length > 0 ? Object.keys(sheetData[0]).map(fieldName => ({
          fieldName,
          fieldType: fieldsType[fieldName] || 'string' // 使用 fieldsType 中的类型，默认 'string'
        })) : [];
      
        return {
          sheetId,
          sheetName,
          sheetFields,
          sheetCollection: sheetCollectionName
        };
      });
      let excelData = {
        excelId: uuid(),
        excelName: params.name,
        createTime: new Date(),
        sheetList,
        id: filter.id // 创建和之前id相同的数据
      }
      const excelRes = await this.ctx.model.Excel.create(excelData)
      this.ctx.logger.info('update更新的excelRes',excelRes);
    }
    return res
  }

  async findOne(filter, projection, options) {
    const res = await this.ctx.model.Datastorage.findOne(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }

  async getDbList(body) {
    const type = body.type || 'static'
    const dbconn = new dbconns[type](body, this)
    const res = await dbconn.dblist()
    if(this.config.PDBC && this.config.PDBC.enable){
      if(type == 'dmdb'){
        return res.rows.map(v => {
          return v[0]
        })
      }
    } else {
      if (type == 'oracle' || type == 'dmdb') {
        return res.rows.map(v => {
          return v[0]
        })
      }
    }
    
    // to_do: 是否需要把mysql的几个系统库给过滤掉
    return res.map(function (val) {
      // return val.schema_name;
      // 好像有部分mysql返回的key不是schema_name,所以用取keys的方式
      return val[Object.keys(res[0])[0]]
    })
  }

  async getData(conf, redisKey) {
    let config = conf
    let component = await this.ctx.model.Component.findOne({
      id: config.componentId
    })
    if (!component) {
      throw new SeatomException(
        ERROR_CODES.COMPONENT_NOT_EXIST,
        'component not exists'
      )
    }
    let type = config.type
      ? config.type
      : component.dataConfig.dataResponse.sourceType
    let isdrillDown =
      config.params &&
      config.params._isDrill &&
      component.interactionConfig &&
      component.interactionConfig.drillDown &&
      component.interactionConfig.drillDown.length > 0 &&
      component.interactionConfig.drillDown[0].drillType &&
      component.interactionConfig.drillDown[0].drillType == 'server'
    const drillDown = component.interactionConfig.drillDown[0]
    let drillPath = []
    if (config.params) {
      drillPath = config.params._drillPath || ['']
    } else {
      drillPath = ['']
    }
    if (type === 'datacontainer') {
      const comId =
        component.dataConfig.dataResponse.source[type].data.dataContainerComId
      component = await this.ctx.model.Component.findOne({
        id: comId
      })
      // 数据容器数据源修改参数
      config.componentId = comId
      type = component.dataConfig.dataResponse.sourceType
      isdrillDown = false
    }

    if (type === 'static') {
      return component.staticData
    } else {
      const componentConfig =
        component.dataConfig.dataResponse.source[type].data
      // dmc不需要先新建数据源
      if (!componentConfig.sourceId && type != 'dmc' && type != 'bdp' ) {
        throw new SeatomException(
          ERROR_CODES.COMPONENT_HAS_NO_SUCH_DATASOURCE,
          'component has no datasource of ' + type
        )
      }
      const baseDatastorage = await this.ctx.model.Datastorage.findOne({
        id: componentConfig.sourceId || 0
      })
      const baseConfig = baseDatastorage ? baseDatastorage.config : {}
      // 对接仪表盘数据start
      if (type === 'dashboard') {
        const dashboardData = await this.ctx.service.datadashboard.getChartData(
          Object.assign(config, baseConfig),
          componentConfig.isLimit,
          componentConfig.limitNum
        )
        return dashboardData
      }
      // 对接仪表盘数据end
      // 数据筛选和后端联动逻辑
      const dbconn = new dbconns[type](Object.assign(config, baseConfig), this)
      if (config.params && config.params.temporary && componentConfig.where) {
        if (componentConfig.where.enable) {
          // componentConfig.where.whereCondition = componentConfig.where.whereCondition.concat(config.params.whereCondition); // 后端联动和数据筛选进行merge
          componentConfig.where.whereCondition = config.params.whereCondition // 后端联动和数据筛选进行merge
        } else {
          componentConfig.where.whereCondition = config.params.whereCondition
          componentConfig.where.enable = true
        }
      }
      let res = []
      try {
        if (type === 'excel') {
          config = Object.assign(config, {isNewFlag: baseDatastorage.isNewFlag, id:baseDatastorage.id})
        }
        const startTime = new Date().getTime()
        res = await dbconn.getData(baseConfig, componentConfig, config)
        const endTime = new Date().getTime()
        this.ctx.logger.info(`getData用的时间：${endTime - startTime}ms`)
      } catch (error) {
        res = { message: error.message, code: 801 }
        return res
      }
      this.ctx.logger.info('res is what and modify length', res.length)
      if (type === 'json') {
        return isdrillDown ? initDrill(res, drillDown, drillPath) : res
      }
      if (type === 'api') {
        return res
      }
      // modify here
      if (type === 'dmc'){
        let flag = true
        let {dataStatus, dataCount} = await this.ctx.service.datastorage.validateStatus(config, componentConfig, 'dmc')
          // 如果 表状态是失败的话
          if(dataStatus && dataStatus == 2){
            flag = false
          }
          // 如果 datacount存在
          if(dataCount != null){
            const newKey = redisKey + 'datacount'
            const lastDataCount = await this.ctx.service.cacheservice.get(newKey)
            if(lastDataCount){
              // 如果上一次 dataCount > 0 这次的为 0了
              if(lastDataCount > 0 && dataCount == 0){
                flag = false
              }
            }
            if(dataCount != 0){
              // 如果 dataCount不为 0，更新 dataCount
              await this.ctx.service.cacheservice.set(newKey, dataCount)
            }
          }
        return {res, flag}
      }
      // 可以截取返回值的数据源
      if (type == 'oracle' || type === 'dmdb') {
        return res.rows.map(v => {
          return res.metaData.reduce((p, key, i) => {
            p[key.name] = v[i]
            return p
          }, {})
        })
      }
      if (componentConfig.isLimit) {
        return isdrillDown
          ? initDrill(
              res.slice(0, componentConfig.limitNum),
              drillDown,
              drillPath
            )
          : res.slice(0, componentConfig.limitNum)
      }
      return isdrillDown
        ? initDrill(res.slice(0, 5000), drillDown, drillPath)
        : res.slice(0, 5000)
    }
  }

  async getFieldList(config) {
    if (config.componentId) {
      const component = await this.ctx.model.Component.findOne({
        id: config.componentId
      })
      if (!component) {
        throw new SeatomException(
          ERROR_CODES.COMPONENT_NOT_EXIST,
          'component not exists'
        )
      }
      const type = config.type
        ? config.type
        : component.dataConfig.dataResponse.sourceType
      if (type === 'static') {
        return component.staticData
      } else {
        const componentConfig =
          component.dataConfig.dataResponse.source[type].data
        if (!componentConfig.sourceId && type != 'dmc') {
          throw new SeatomException(
            ERROR_CODES.COMPONENT_HAS_NO_SUCH_DATASOURCE,
            'component has no datasource of ' + type
          )
        }
        const baseDatastorage = await this.ctx.model.Datastorage.findOne({
          id: componentConfig.sourceId || 0
        })
        const baseConfig = baseDatastorage ? baseDatastorage.config : {}
        config = Object.assign(config, baseConfig)
        if (type === 'excel') {
          config = Object.assign(config, {isNewFlag: baseDatastorage.isNewFlag, id:baseDatastorage.id})
          console.log('New config', config);
        }
      }
    }
    const dbconn = new dbconns[config.type](config, this)
    const res = await dbconn.getFieldList(config)
    return res
  }

  async getTreeList(config) {
    console.log(config,'-----------')
    if (config.componentId) {
      const component = await this.ctx.model.Component.findOne({
        id: config.componentId
      })
      if (!component) {
        throw new SeatomException(
          ERROR_CODES.COMPONENT_NOT_EXIST,
          'component not exists'
        )
      }
      const type = config.type
        ? config.type
        : component.dataConfig.dataResponse.sourceType
      if (type === 'static' || type == 'dmc' || type == 'bdp') {
        console.log('123123')
        return ''
        
      } else {
        const componentConfig =
          component.dataConfig.dataResponse.source[type].data
        // dmc不需要先新建数据源
        if (!componentConfig.sourceId) {
          throw new SeatomException(
            ERROR_CODES.COMPONENT_HAS_NO_SUCH_DATASOURCE,
            'component has no datasource of ' + type
          )
        }
        const baseDatastorage = await this.ctx.model.Datastorage.findOne({
          id: componentConfig.sourceId || 0
        })
        const baseConfig = baseDatastorage ? baseDatastorage.config : {}
        config = Object.assign(config, baseConfig)
        // 如果数据源是excel，添加isNewFlag和excelBase索引的id到config里
        if (type === 'excel') {
          config = Object.assign(config, {isNewFlag: baseDatastorage.isNewFlag, id:baseDatastorage.id})
          console.log('New config', config);
        }
      }
    }
    const dbconn = new dbconns[config.type](config, this)
    let res = dbconn.getTreeList(config)
    if (config.type == 'oracle') {
      res = await dbconn.getTreeList(config)
      return res.rows.map(v => {
        return res.metaData.reduce((p, key, i) => {
          p['tablename'] = v[i]
          return p
        }, {})
      })
    }
    return res
  }

  async getFolderTbList(config) {
    const dbconn = new dbconns[config.type](config, this)
    const res = dbconn.getFolderTbList(config)
    return res
  }

  async searchTb(config) {
    const dbconn = new dbconns[config.type](config, this)
    const res = dbconn.searchTb(config)
    return res
  }

  async preview(config) {
    let prevConfig = null // 创建数据源之前先进行预览
    if (config && config.filePath) {
      prevConfig = {
        filePath: config.filePath
      }
      const dbconn = new dbconns[config.type](config, this)
      const res = dbconn.preview(prevConfig || baseDatastorage.config)
      return res
    }
    const baseDatastorage = await this.ctx.model.Datastorage.findOne({
      id: config.storageId || 0,
      type: config.type
    })
    if (!baseDatastorage) {
      throw new SeatomException(
        ERROR_CODES.DATASTORAGE_NOT_EXIST,
        'datastorage not exists'
      )
    }
    const dbconn = new dbconns[config.type](config, this)
    const res = dbconn.preview(baseDatastorage.config)
    return res
  }

  async getFunctionList(config) {
    const dbconn = new dbconns[config.type](config, this)
    const res = await dbconn.getFunctionList()
    return res
  }

  async clearCache(config) {
    const REDIS_PORT = this.app.config.redis.clients.db0.port
    const REDIS_HOST = this.app.config.redis.clients.db0.host
    const REDIS_PWD = this.app.config.redis.clients.db0.password
    const client = redis.createClient(REDIS_PORT, REDIS_HOST)
    client.auth(REDIS_PWD, (err) => {
      if (err) {
        console.error('redis校验失败', err);
      }
    });
    const keysAsync = promisify(client.keys).bind(client);
    const delAsync = promisify(client.del).bind(client);
    const keyPrefix = `sql${config.screenId}`;
    const keys = await keysAsync(keyPrefix + '*')
    console.log('targetData###',keys);
    // 如果找不到对应的key，说明没有缓存的redis数据
    if(keys.length == 0 ) {
      throw new SeatomException(705, '未查询到有缓存数据！')
    } else {
      const delCount = await delAsync(keys);
      const result = await this.service.cacheservice.collectDel(keys)
      return `删除成功, 删除了${delCount}个键,${result}`;
    }
  }

  async validateSql(config) {
    const sql = config.params.targetSql
    console.log('sql#####',sql);
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (let i = 0; i < ast.length; i++) {
          if (ast[i].type !== 'select') {
            throw new Error()
          }
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      console.log('error####',error);
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        'sql格式错误'
      )
    }
    return true
  }

  async validateStatus(config, componentConfig, type) {
    this.ctx.logger.info('config from validateStatus in Service',config);
    const dbconn = new dbconns[type](config, this)
    const res = dbconn.validateTbStatus(componentConfig)
    return res
  }
}

module.exports = DataStorageService

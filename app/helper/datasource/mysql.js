const utils = require('util')
const DbConn = require('../base-datasource')
const mysql = require('mysql')
const crypto = require('crypto')
const CacheService = require('../../service/cacheservice')
const moment = require('moment')
const { findIndex, cond } = require('lodash')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')
const {
  buildSelectSql,
  replaceNameToFid,
  generateSign,
  wrapAggregate,
  compare,
  formattedDate
} = require('../../extend/sql-utils')
const { Parser } = require('node-sql-parser')
const parser = new Parser()

const { MYSQL_TYPES } = require('../../extend/constant')
const { resolveSoa } = require('dns')
function Mysql(config, service) {
  this.config = {
    host: String(config.host) || 'localhost',
    user: String(config.username) || 'root',
    password: String(config.password) || '',
    port: config.port || 3306,
    database: config.database,
    timezone: '08:00',
    multipleStatements: false // 一次执行一条sql，防止sql注入
  }
  this.service = service
  this.config = Object.assign(config, this.config)
}

Mysql.prototype._request = async function (url, sql) {
  const { host, port, user, password, database } = this.config
  const { ctx } = this.service
  const base = this.service.config.PDBC.address
  const connStr = `jdbc:mysql://${host}:${port}?useCursorFetch=true&useUnicode=true&autoReconnect=true&useSSL=false`
  let params = {
    conn_str: connStr,
    driver_str: 'com.mysql.cj.jdbc.Driver',
    driver_jar: '',
    username: user,
    password
  }
  console.log('params===',params);
  if (sql) {
    params = Object.assign(params, { sql })
    params.conn_str = `jdbc:mysql://${host}:${port}/${database}?useCursorFetch=true&useUnicode=true&autoReconnect=true&useSSL=false`
  }
  let result;
  try {
    result = await ctx.curl(base + url, {
      headers: {
        'Content-Type': 'application/json'
      },
      dataType: 'json',
      data: params,
      method: 'POST',
      timeout: 5 * 60 * 1000
    })
  } catch (error) {
    ctx.logger.info('jdbc接口报错', error)
    throw new SeatomException(400, error)
  }
  ctx.logger.info('_request返回值', result)
  if (result.data.status == 'success') {
    return result.data.result
  } else {
    throw new SeatomException(+result.code, result.data.reason)
  }
}
utils.inherits(Mysql, DbConn)
Mysql.prototype.getFieldList = async function (config) {
  const dateFields = [],
    stringFields = [],
    numberFields = []
  if (this.service.config.PDBC && this.service.config.PDBC.enable) {
    const url = `/sqltree/columns?catalog=${config.database}&schema=&table=${config.tbName}`
    const result = await this._request(url)
    let list = Object.keys(result)
    list.forEach((key) => {
      if (result[key].type == 'string') {
        stringFields.push({name:result[key].name, data_type:result[key].raw_type})
      }
      if (result[key].type == 'number') {
        numberFields.push({name:result[key].name, data_type:result[key].raw_type})
      }
      if (result[key].type == 'date') {
        dateFields.push({name:result[key].name, data_type:result[key].raw_type})
      }
    })
  } else {
    const sql = `select column_name as name, data_type as data_type from information_schema.columns where table_schema ='${config.database}' and table_name = '${config.tbName}' ;`
    // 处理返回的数据类型
    const res = await this.execute(sql)
    res.map((item, value) => {
      if (MYSQL_TYPES.STRING_TYPE.includes(item.data_type.toUpperCase())) {
        stringFields.push(item)
      }
      if (MYSQL_TYPES.NUMBER_TYPE.includes(item.data_type.toUpperCase())) {
        numberFields.push(item)
      }
      if (MYSQL_TYPES.DATE_TYPE.includes(item.data_type.toUpperCase())) {
        dateFields.push(item)
      }
    })
  }
  const fieldList = []
  fieldList.push({
    type: 'date',
    fields: dateFields
  })
  fieldList.push({
    type: 'string',
    fields: stringFields
  })
  fieldList.push({
    type: 'number',
    fields: numberFields
  })
  return fieldList
}

Mysql.prototype.getTreeList = async function (config) {
  if (this.service.config.PDBC && this.service.config.PDBC.enable){
    const url = `/sqltree/tables?catalog=${this.config.database}&schema=`
    const res = await this._request(url)
    const finalRes = res.map((item)=>{
      return {TABLE_NAME: item}
    })
    return finalRes
  } else {
  const sql = `select table_name as \`TABLE_NAME\` from information_schema.tables where table_schema='${this.config.database}'`
  const res = await this.execute(sql)
  console.log('res===',res);
  return res
  }
}

Mysql.prototype.dblist = async function () {
  if (this.service.config.PDBC && this.service.config.PDBC.enable){
    const url = '/sqltree/catalogs'
    const res = await this._request(url)
    const finalRes = res.map((item)=>{
      return {schema_name: item}
    })
    return finalRes
  } else {
    const sql = 'select schema_name from information_schema.schemata;'
    const res = await this.execute(sql)
    return res
  }
  
}

Mysql.prototype.hanleData = async function (
  baseConfig,
  componentConfig,
  queryParams,
  dateBaseType
) {
  // 记录四个模块的内容
  let selectList = [],
    whereList = '',
    groupByList = [],
    orderByList = []
  if (!queryParams.workspaceId && queryParams.workspaceId !== 0) {
    throw new SeatomException(
      ERROR_CODES.PARAMETER_IS_REQUIRED,
      '缺失workspaceId参数'
    )
  }
  // 处理维度字段列表
  const fieldList = []
  let datefile = []
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if (element.name) {
      if (element.original_name) {
        if (element.name == element.original_name){
          // 如果同名，就不加AS了
          selectList.push('`' + element.original_name + '`')
        } else {
          selectList.push(
            '`' + element.original_name + '` AS `' + element.name + '`'
          )
          groupByList.push('`' + element.original_name + '`')
          // orderByList.push('`' + element.original_name + '`')
        }
      } else {
        selectList.push('`' + element.name + '` AS `' + element.name + '`')
        groupByList.push('`' + element.name + '`')
        // orderByList.push('`' + element.name + '`')
      }
      if (element.data_type == 'datetime') {
        datefile.push({
          name: element.name,
          dateFormatter: element.dateFormatter
        })
      }
      // 有别名加别名，没有别名字段先加名称
      fieldList.push('`' + element.name + '`')
    }
  })

  // 处理数值字段列表
  let numFile = []
  const numerical = componentConfig.fields.numericalValue
  let hasNumField = false
  if(numerical.length){
    hasNumField = true
  }
  numerical.forEach((element, index) => {
    if (element.name && element.calculation) {
      let fieldStr
      if (element.original_name) {
        fieldStr = wrapAggregate(
          element.calculation,
          '`' + element.original_name + '`',
          dateBaseType,
          element.data_type,
          element.name + '(' + element.calculation + ')'
        )
      } else {
        fieldStr = wrapAggregate(
          element.calculation,
          '`' + element.name + '`',
          dateBaseType,
          element.data_type
        )
      }
      numFile.push({
        name: element.name + '(' + element.calculation + ')',
        formatter: element.formatter
      })
      selectList.push(fieldStr)
    }
  })
  if (selectList.length == 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  }
  const where = componentConfig.where
  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]
  if (where.enable) {
    console.log('where.wherecondition',where.whereCondition);
    // 对whereCondition做预处理，如果是
    // for (let i = 0; i < where.whereCondition.length; i++) {
    //   const item = where.whereCondition[i];
    //   if (item.type === 'string' && item.compare === 'notNull') {
    //     where.whereCondition.splice(i + 1, 0, {
    //       field: item.field,
    //       type: 'string',
    //       fid: item.fid,
    //       compareValue: [],
    //       composeType: item.composeType,
    //       compare: 'notEmpty'
    //     });
    //     item.composeType = 'and';
    //     i++; // 跳过新插入的元素
    //   }
    // }
    where.whereCondition.forEach((item, index) => {
      // 处理筛选条件，包括今天、昨天、最近7天、最近30天、最近90天、最近一年、全部
      if (relative.includes(item.compare)) {
        item.compareValue = this.relativeDate(item)
      }
      {
        if (
          item.compare == 'unequal' &&
          dateBaseType == 'mysql' &&
          item.type == 'date'
        ) {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "IFNULL('" +
                item.field +
                "',' ')" +
                ' '
              : "IFNULL('" + item.field + "',' ')" + ' '
        } else if (item.compare == 'contained') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "'" +
                item.compareValue[0] +
                "'"
              : "'" + item.compareValue[0] + "'"
        } else if (item.compare == 'notNull' && item.type == 'string'){
          whereSql += index == 0 ? ` ${generateSign(where.whereCondition.length)} `
          +'(' + '`' + item.field + '`' + 'is not null AND ' + '`' + item.field + '`'
          + `!= '')` : '(' + '`' + item.field + '`' + 'is not null AND ' + '`' + item.field + '`'
          + `!= '')`
          ;
        } else {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                '`' +
                item.field +
                '`' +
                ' '
              : '`' + item.field + '`' + ' '
        }
      }
        switch (item.compareValue.length) {
          case 0:
            if(item.compare == 'notNull' && item.type == 'string'){
              whereSql +=
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            break
            } else {
              whereSql +=
              compare[item.type][item.compare] +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
              break
            }
            
          case 1:
            if (item.type == 'string') {
              if (
                ['matchOnEnd', 'matchOnStart', 'notContain', 'contain'].includes(
                  item.compare
                )
              ) {
                if (dateBaseType == 'mysql') {
                  whereSql +=
                    compare[item.type][item.compare].replace(
                      '{0}',
                      `${item.compareValue[0]}`
                    ) +
                    (where.whereCondition.length == index + 1
                      ? ' '
                      : item.composeType == 'or'
                      ? ' ) OR '
                      : ' ) AND ')
                } else {
                  whereSql +=
                    compare[item.type][item.compare].replace(
                      '{0}',
                      `'${item.compareValue[0]}'`
                    ) +
                    (where.whereCondition.length == index + 1
                      ? ' '
                      : item.composeType == 'or'
                      ? ' ) OR '
                      : ' ) AND ')
                }
              } else if (item.compare == 'contained') {
                whereSql += compare[item.type][item.compare].replace(
                  '{0}',
                  `${item.fid}`
                )
                ;+(where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
              } else {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `'${item.compareValue[0]}'`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
                  console.log('whereSql===',whereSql);
              }
            }
            if (item.type == 'date') {
              const compareValue = moment(item.compareValue[0]).format(
                'YYYY-MM-DD HH:mm:ss'
              )
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  `'${compareValue}'`
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
            if (item.type == 'number') {
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  item.compareValue[0]
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
            break
            case 2:
              let compareValue1, compareValue2
              if (item.type == 'date') {
                compareValue1 = JSON.stringify(
                  moment(item.compareValue[0]).format('YYYY-MM-DD HH:mm:ss')
                )
                compareValue2 = JSON.stringify(
                  moment(item.compareValue[1]).format('YYYY-MM-DD HH:mm:ss')
                )
              } else {
                compareValue1 = item.compareValue[0]
                compareValue2 = item.compareValue[1]
              }
              if (item.type == 'string' && item.compare == 'range') {
                // 范围筛选
                const inItem = JSON.stringify(item.compareValue).replace(
                  /\[|]/g,
                  ''
                )
                whereSql += compare[item.type][item.compare].replace(
                  '{0}',
                  inItem
                )
                +(where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
              } else if (relative.includes(item.compare)) {
                // 日期范围筛选
                whereSql +=
                  compare[item.type][item.compare]
                    .replace('{0}', compareValue1)
                  .replace('{1}', item.field)
                    .replace('{2}', compareValue2) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              } else if (item.type == 'string' && item.compare == 'in') {
                // in筛选
                const inItem = JSON.stringify(item.compareValue).replace(/\[|]/g, '')
                whereSql += compare[item.type][item.compare].replace('{0}', inItem)
                +(where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
              } else {
                // contain筛选
                whereSql +=
                  compare[item.type][item.compare]
                    .replace('{0}', compareValue1)
                    .replace('{1}', compareValue2) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              }
              break
          }
          if (item.compareValue.length > 2) {
            const inItem = JSON.stringify(item.compareValue).replace(/\[|]/g, '')
            whereSql += compare[item.type][item.compare].replace('{0}', inItem)
            +(where.whereCondition.length == index + 1
              ? ' '
              : item.composeType == 'or'
              ? ' ) OR '
              : ' ) AND ')
          }
      console.log('whereSql===',whereSql);
    })

    whereList = whereSql
    // 兼容数据处理功能对orderby
    if (where.orderCondition && where.orderCondition.length) {
      where.orderCondition.forEach((item, index) => {
        if (item.calculation) {
          // 按年、月、日、时、分、秒
          const field = calulateDate(item.calculation, item.fid)
          orderByList.unshift(field + ' ' + item.orderBy)
        } else {
          const orderByListIndex = findIndex(orderByList, item.field)
          if (orderByListIndex > -1) {
            delete orderByList[orderByListIndex]
            orderByList.unshift(item.field + ' ' + item.orderBy)
          } else {
            orderByList.unshift(item.field + ' ' + item.orderBy)
          }
        }
      })
    } else {
      orderByList = []
    }

    orderByList.forEach((item, index) => {
      if (item.search('desc') == -1 && item.search('asc') == -1) {
        orderByList[index] = orderByList[index] + ' ' + ' asc '
      }
    })
  } else {
    whereList = ''
    orderByList = []
  }
  // this.service.ctx.logger.info('whereList-------------', whereList)
  const limitNum = componentConfig.isLimit ? componentConfig.limitNum : 500
  const sql = buildSelectSql(
    componentConfig.tbName,
    selectList,
    whereList,
    groupByList,
    orderByList,
    limitNum,
    queryParams.type || dateBaseType,
    hasNumField
  )
  // console.log('>>>>>>>><<<<<<<<<<>>>>>', typeof sql, sql)
  return {
    sql,
    numFile,
    datefile
  }
}

Mysql.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {
  let sql
  if (componentConfig.advanced) {
    sql = queryParams.params.targetSql || componentConfig.sql
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (let i = 0; i < ast.length; i++) {
          if (ast[i].type !== 'select') {
            throw new Error()
          }
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        'sql格式错误'
      )
    }
    // 拼上筛选条件
    let screenSQl = hanleScreen(
      baseConfig,
      componentConfig,
      queryParams,
      'mysql'
    )
    sql = joinWhere(sql, screenSQl.whereList)
  } else {
    const obj = await this.hanleData(
      baseConfig,
      componentConfig,
      queryParams,
      'mysql'
    )
    sql = obj.sql
    numFile = obj.numFile
    datefile = obj.datefile
  }
  let mysqlResult
  if( queryParams.env === 'dev' && this.service.config.mysqlCache && this.service.config.mysqlCache.enable ) {
    const redis = this.service.ctx.service.cacheservice
    const key = await redis.keyProcess(sql, queryParams.screenId)
    const result = await redis.lruGet(key)
    this.service.ctx.logger.info(result,'查询redis的结果')
    // 如果result存在,替换答案，不做请求
    if(result){
      mysqlResult = JSON.parse(result)
      this.service.ctx.set('hz-data-catch', key)
    } else {
      try {
        mysqlResult = await this.query(sql)
      } catch (error) {
        throw new SeatomException(500,error)
      }
      await redis.lruSet(key, JSON.stringify(mysqlResult))
      this.service.ctx.logger.info('没值，重新获取，查询的结果是',mysqlResult)
    }
  } else {
  try {
    mysqlResult = await this.query(sql)
  } catch (error) {
    throw new SeatomException(500, 'mysql的getData报错')
  }
  }
  // this.service.ctx.logger.info('mysqlresul######t', mysqlResult)
  for (let index = 0; index < mysqlResult.length; index++) {
    const mysqlitem = mysqlResult[index]
    for (let d = 0; d < datefile.length; d++) {
      const dateitem = datefile[d]
      if (mysqlitem.hasOwnProperty(dateitem.name) && dateitem.dateFormatter) {
        mysqlResult[index][dateitem.name] = formattedDate(
          mysqlitem[dateitem.name],
          dateitem.dateFormatter
        )
      }
    }
    for (let d = 0; d < numFile.length; d++) {
      const numFiletem = numFile[d]
      if (
        mysqlitem.hasOwnProperty(numFiletem.name) &&
        numFiletem.formatter &&
        numFiletem.formatter.num.digit &&
        numFiletem.formatter.num.unit
      ) {
        mysqlResult[index][numFiletem.name] = (
          mysqlitem[numFiletem.name] / numFiletem.formatter.num.unit
        ).toFixed(numFiletem.formatter.num.digit)
      }
    }
  }
  return mysqlResult
}

function handlegetData(data) {
  if(data.schema.length > 0 && data.data.length > 0) {
    let res = data.data.map((item)=> {
      const obj = {}
      data.schema.forEach((it,index) => {
        obj[it.name] = item[index]
      })
      return obj
    })
    return res
  }
}

Mysql.prototype.query = async function (sql) {
  // 校验sql变量格式
  if (!sql || typeof sql !== 'string') {
    throw new Error('sql statement format is illegal')
  }

  // 去掉sql两端空格
  sql = sql.replace(/^\s+|\s+$/g, '')

  // 做一个简单的sql校验，防止用户写了个drop之类的东西搞事情
  if (sql.substring(0, 6).toLowerCase() != 'select') {
    throw new Error("query sql must start with 'select'")
  }
  this.service.ctx.logger.info(sql, 'sql')
  console.log('this.service.config.PDBC && this.service.config.PDBC.enable',this.service.config.PDBC && this.service.config.PDBC.enable);
  if(this.service.config.PDBC && this.service.config.PDBC.enable){
    console.log('走PDBC',);
    const shortUrl = '/sqltree/query'
    const res = handlegetData(await this._request(shortUrl, sql))
    console.log('res====',res);
    return res
  } else {
    const res = await this.execute(sql)
    return res
  }
  
}

Mysql.prototype.execute = function (sql) {
  // to_do: 目前为了防止连接数过多，采取查完即关闭连接，需要优化
  const connection = mysql.createConnection(this.config)
  return new Promise(function (resolve, reject) {
    connection.connect(function (err) {
      if (err) {
        reject(err)
        return
      }
    })

    connection.query(sql, function (error, res) {
      if (error) {
        reject(error)
        return
      }
      resolve(res)
    })
    connection.end()
  })
}

exports = module.exports = Mysql

function hanleScreen(baseConfig, componentConfig, queryParams, dateBaseType) {
  // 记录四个模块的内容
  let selectList = [],
    whereList = '',
    groupByList = [],
    orderByList = []
  if (!queryParams.workspaceId && queryParams.workspaceId !== 0) {
    throw new SeatomException(
      ERROR_CODES.PARAMETER_IS_REQUIRED,
      '缺失workspaceId参数'
    )
  }
  // 处理维度字段列表
  const fieldList = []
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if (element.name) {
      selectList.push('`' + element.name + '`')
      groupByList.push('`' + element.name + '`')
      // orderByList.push('`' + element.name + '`')
      // 有别名加别名，没有别名字段先加名称
      fieldList.push('`' + element.name + '`')
    }
  })

  // 处理数值字段列表
  const numerical = componentConfig.fields.numericalValue
  numerical.forEach((element, index) => {
    if (element.name && element.calculation) {
      const fieldStr = wrapAggregate(
        element.calculation,
        '`' + element.name + '`',
        dateBaseType,
        element.data_type
      )
      selectList.push(fieldStr)
    }
  })
  // if (selectList.length == 0) {
  //   throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  // }
  const where = componentConfig.where
  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]
  if (where.enable) {
    console.log('where.wherecondition', where.whereCondition)
    where.whereCondition.forEach((item, index) => {
      // 处理筛选条件，包括今天、昨天、最近7天、最近30天、最近90天、最近一年、全部
      if (relative.includes(item.compare)) {
        item.compareValue = this.relativeDate(item)
      }
      {
        if (
          item.compare == 'unequal' &&
          dateBaseType == 'mysql' &&
          item.type == 'date'
        ) {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "IFNULL('" +
                item.field +
                "',' ')" +
                ' '
              : "IFNULL('" + item.field + "',' ')" + ' '
        } else if (item.compare == 'contained') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "'" +
                item.compareValue[0] +
                "'"
              : "'" + item.compareValue[0] + "'"
        } else {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                '`' +
                item.field +
                '`' +
                ' '
              : '`' + item.field + '`' + ' '
        }
      }
      switch (item.compareValue.length) {
        case 0:
          whereSql +=
            compare[item.type][item.compare] +
            (where.whereCondition.length == index + 1
              ? ' '
              : item.composeType == 'or'
              ? ' ) OR '
              : ' ) AND ')
          break
        case 1:
          if (item.type == 'string') {
            if (
              ['matchOnEnd', 'matchOnStart', 'notContain', 'contain'].includes(
                item.compare
              )
            ) {
              if (dateBaseType == 'mysql') {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `${item.compareValue[0]}`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              } else {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `'${item.compareValue[0]}'`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              }
            } else if (item.compare == 'contained') {
              whereSql +=
                compare[item.type][item.compare].replace('{0}', `${item.fid}`) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            } else {
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  `'${item.compareValue[0]}'`
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
          }
          if (item.type == 'date') {
            const compareValue = moment(item.compareValue[0]).format(
              'YYYY-MM-DD HH:mm:ss'
            )
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                `'${compareValue}'`
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          if (item.type == 'number') {
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                item.compareValue[0]
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
        case 2:
          let compareValue1, compareValue2
          if (item.type == 'date') {
            compareValue1 = JSON.stringify(
              moment(item.compareValue[0]).format('YYYY-MM-DD HH:mm:ss')
            )
            compareValue2 = JSON.stringify(
              moment(item.compareValue[1]).format('YYYY-MM-DD HH:mm:ss')
            )
          } else {
            compareValue1 = item.compareValue[0]
            compareValue2 = item.compareValue[1]
          }
          if (relative.includes(item.compare)) {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', item.field)
                .replace('{2}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          } else {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
      }
    })

    whereList = whereSql
    // 兼容数据处理功能对orderby
    if (where.orderCondition && where.orderCondition.length) {
      where.orderCondition.forEach((item, index) => {
        if (item.calculation) {
          // 按年、月、日、时、分、秒
          const field = calulateDate(item.calculation, item.fid)
          orderByList.unshift(field + ' ' + item.orderBy)
        } else {
          const orderByListIndex = findIndex(orderByList, item.field)
          if (orderByListIndex > -1) {
            delete orderByList[orderByListIndex]
            orderByList.unshift(item.field + ' ' + item.orderBy)
          } else {
            orderByList.unshift(item.field + ' ' + item.orderBy)
          }
        }
      })
    } else {
      orderByList = []
    }

    orderByList.forEach((item, index) => {
      if (item.search('desc') == -1 && item.search('asc') == -1) {
        orderByList[index] = orderByList[index] + ' ' + ' asc '
      }
    })
  } else {
    whereList = ''
    orderByList = []
  }
  // this.service.ctx.logger.info(
  //   'whereList-------------',
  //   whereList,
  //   'whereList-------------',
  //   orderByList
  // )
  // const sql = buildSelectSql(
  //   componentConfig.tbName,
  //   selectList,
  //   whereList,
  //   groupByList,
  //   orderByList,
  //   100000,
  //   queryParams.type || dateBaseType
  // )
  // console.log('>>>>>>>><<<<<<<<<<>>>>>', typeof sql, sql)
  return {
    whereList
  }
}
// 拼接wherelist条件
function joinWhere(query, filter) {
  // 如果query或filter为空，直接返回query
  if (!query || !filter) {
    return query
  }
  // 去掉query末尾的分号（如果有的话）
  query = query.trim()
  if (query.endsWith(';')) {
    query = query.slice(0, -1)
  }
  // 根据sql语法，直接把要写在where后面的语法直接全部截取出来
  // 匹配where后面的所有子句（order by, limit, group by, having等）
  let afterWhereRegex = /(\s+(order|limit|group|having)\s+.*)/i
  let afterWhereMatch = query.match(afterWhereRegex)
  let afterWherePart = afterWhereMatch ? afterWhereMatch[0] : '' // where后面的所有子句的字符串（如果有的话）
  let beforeWherePart = query // where前面的所有子句的字符串（默认为整个查询语句）
  // 如果有where后面的子句，则从query中截取掉
  if (afterWherePart) {
    beforeWherePart = query.slice(0, -afterWherePart.length)
  }

  // 然后把where按条件插入进去，再拼接之前截取出的
  if (beforeWherePart.toLowerCase().includes('where')) {
    // 如果有where部分，说明已经有过滤条件
    // 则在where部分后面用and连接新的过滤条件
    return beforeWherePart + ' and ' + filter + afterWherePart + ';'
  } else {
    // 如果没有where部分，说明没有过滤条件
    // 则在beforeWherePart的末尾添加where子句和过滤条件
    return beforeWherePart + ' where ' + filter + afterWherePart + ';'
  }
}

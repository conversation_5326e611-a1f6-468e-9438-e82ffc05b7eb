var utils = require('util')
const DbConn = require('../base-datasource')
// var oracledb = require('oracledb')
const { ERROR_CODES, ORACLE_TYPES } = require('../../extend/constant')
const SeatomException = require('../../exception/seatomException')
const { Parser } = require('node-sql-parser')
const { formattedDate } = require('../../extend/sql-utils')
const parser = new Parser()

function Oracle(config, service) {
  this.config = {
    user: String(config.username || 'system'),
    password: String(config.password || 'oracle'),
    connectString:
      String(config.host || '127.0.0.1') +
      ':' +
      Number(config.port || 1521) +
      '/' +
      (config.sid || '')
  }
  delete config.username
  this.service = service
  this.config = Object.assign(config, this.config)
}

utils.inherits(Oracle, DbConn)

function handlegetData(data) {
  if(data.schema.length > 0 && data.data.length > 0) {
    let res = data.data.map((item)=> {
      const obj = {}
      data.schema.forEach((it,index) => {
        obj[it.name] = item[index]
      })
      return obj
    })
    console.log('处理后的res',res);
    return res
  }
}

Oracle.prototype._request = async function (queryUrl, sql) {
  const { ctx } = this.service
  const base = this.service.config.PDBC.address
  const conn_str = `jdbc:oracle:thin:@//${this.config.connectString}`
  const params = {
    conn_str,
    driver_str: "oracle.jdbc.OracleDriver",
    username: this.config.user,
    password: this.config.password,
    sql
  }
  const url = base + queryUrl
  try {
    result = await ctx.curl(url, {
      headers: {
        'Content-Type': 'application/json'
      },
      dataType: 'json',
      data: params,
      method: 'POST',
      timeout: 5 * 60 * 1000
    })
  } catch (error) {
    ctx.logger.info('jdbc接口报错', params, error)
    throw new SeatomException(400, error)
  }
  ctx.logger.info('_request返回值', result, result.data.status)
  if (result.data.status == 'success') {
    return result.data.result
  } else {
    throw new SeatomException(+result.code, result.data.reason)
  }
}

Oracle.prototype.getFieldList = async function (config) {
  const sql = `select COLUMN_NAME,DATA_TYPE from user_tab_columns where table_name='${config.tbName}'`
  const newsql = `SELECT t.owner AS table_schem,
    t.column_name AS column_name,
DECODE(  (SELECT a.typecode
  FROM ALL_TYPES A
  WHERE a.type_name = t.data_type),
'OBJECT', 2002,
'COLLECTION', 2003,
DECODE(substr(t.data_type, 1, 9),
 'TIMESTAMP',
   DECODE(substr(t.data_type, 10, 1),
     '(',
       DECODE(substr(t.data_type, 19, 5),
         'LOCAL', -102, 'TIME ', -101, 93),
     DECODE(substr(t.data_type, 16, 5),
       'LOCAL', -102, 'TIME ', -101, 93)),
 'INTERVAL ',
   DECODE(substr(t.data_type, 10, 3),
    'DAY', -104, 'YEA', -103),
 DECODE(t.data_type,
   'BINARY_DOUBLE', 101,
   'BINARY_FLOAT', 100,
   'BFILE', -13,
   'BLOB', 2004,
   'CHAR', 1,
   'CLOB', 2005,
   'COLLECTION', 2003,
   'DATE', 93,
   'FLOAT', 6,
   'LONG', -1,
   'LONG RAW', -4,
   'NCHAR', -15,
   'NCLOB', 2011,
   'NUMBER', 2,
   'NVARCHAR', -9,
   'NVARCHAR2', -9,
   'OBJECT', 2002,
   'OPAQUE/XMLTYPE', 2009,
   'RAW', -3,
   'REF', 2006,
   'ROWID', -8,
   'SQLXML', 2009,
   'UROWID', -8,
   'VARCHAR2', 12,
   'VARRAY', 2003,
   'XMLTYPE', 2009,
   1111)))
AS data_type,
    t.data_type AS type_name,
    t.column_id AS ordinal_position
FROM all_tab_cols t
WHERE t.table_name = '${config.tbName}'
ORDER BY table_schem, table_name, ordinal_position`
const url = '/sqltree/query'
  const target = await this._request(url, newsql)
  const result = handlegetData(target)
  console.log('1231312313',result);
  // const res = await this.execute(newsql)
  let dateFields = [],
    stringFields = [],
    numberFields = []
  result.map((item, value) => {
    if (ORACLE_TYPES.STRING_TYPE.includes(item.TYPE_NAME)) {
      stringFields.push({ name: item.COLUMN_NAME, data_type: item.TYPE_NAME })
    }
    if (ORACLE_TYPES.NUMBER_TYPE.includes(item.TYPE_NAME)) {
      numberFields.push({ name: item.COLUMN_NAME, data_type: item.TYPE_NAME })
    }
    if (ORACLE_TYPES.DATE_TYPE.includes(item.TYPE_NAME)) {
      dateFields.push({ name: item.COLUMN_NAME, data_type: item.TYPE_NAME })
  }
})
  const fieldList = []
  fieldList.push({
    type: 'date',
    fields: dateFields
  })
  fieldList.push({
    type: 'string',
    fields: stringFields
  })
  fieldList.push({
    type: 'number',
    fields: numberFields
  })
  return fieldList
}

Oracle.prototype.dblist = async function () {
  // 查询数据库名
  // 这个sql查的是当前用户的数据库，通常只能查一个出来
  const sql = ' select ora_database_name as name from dual '
  const url = '/sqltree/query'
  const res = await this._request(url, sql)
  const result = handlegetData(res)
  return result
}

Oracle.prototype.getTreeList = async function (config) {
  // 视图和表的所有名称
  const sql = ` select table_name as tablename from all_tab_comments `
  const url = '/sqltree/query'
  const res = await this._request(url, sql)
  const result = handlegetData(res).map(item => {
    return {tablename:item.TABLENAME}
  })
  return result
}

Oracle.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {
  let sql, numFile, datefile
  if (componentConfig.advanced) {
    sql = queryParams.params.targetSql || componentConfig.sql
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (let i = 0; i < ast.length; i++) {
          if (ast[i].type !== 'select') {
            throw new Error()
          }
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        'sql格式错误'
      )
    }
  } else {
    const obj = await this.hanleData(
      baseConfig,
      componentConfig,
      queryParams,
      'oracle'
    )
    sql = obj.sql
    numFile = obj.numFile
    datefile = obj.datefile
    console.log(obj, sql)
  }
  // try {
  //   const mysqlResult = await this.query(`select id from "new_227_oracle"`)
  //   console.log(mysqlResult)
  // } catch (error) {
  //   console.log(error)
  // }
  const mysqlResult = await this.query(sql)

  for (let index = 0; index < mysqlResult.length; index++) {
    const mysqlitem = mysqlResult[index]
    for (let d = 0; d < datefile.length; d++) {
      const dateitem = datefile[d]
      if (mysqlitem.hasOwnProperty(dateitem.name) && dateitem.dateFormatter) {
        mysqlResult[index][dateitem.name] = formattedDate(
          mysqlitem[dateitem.name],
          dateitem.dateFormatter
        )
      }
    }
    for (let d = 0; d < numFile.length; d++) {
      const numFiletem = numFile[d]
      if (
        mysqlitem.hasOwnProperty(numFiletem.name) &&
        numFiletem.formatter &&
        numFiletem.formatter.num.digit &&
        numFiletem.formatter.num.unit
      ) {
        mysqlResult[index][numFiletem.name] = (
          mysqlitem[numFiletem.name] / numFiletem.formatter.num.unit
        ).toFixed(numFiletem.formatter.num.digit)
      }
    }
  }
  return mysqlResult
}

Oracle.prototype.query = async function (sql) {
  if (!sql || typeof sql != 'string') {
    throw new Error('sql statement format is illegal')
  }

  sql = sql.replace(/^\s+|\s+$/g, '')

  if (sql.substring(0, 6).toLowerCase() != 'select') {
    throw new Error("query sql must start with 'select'")
  }
  const url = '/sqltree/query'
  const res = await this._request(url, sql)

  
  return res
}

Oracle.prototype.execute = function (sql) {
  const _this = this
  return new Promise(function (resolve, reject) {
    console.warn(_this.config)
    oracledb.createPool(_this.config, (err, pool) => {
      if (err) {
        reject(err)
        return
      }
      pool.getConnection((error, connection) => {
        if (error) {
          reject(error)
          return
        }
        connection.execute(sql, (e, data) => {
          if (e) {
            reject(e)
            return
          }
          resolve(data)
          connection.close()
        })
      })
    })
  })
}

exports = module.exports = Oracle

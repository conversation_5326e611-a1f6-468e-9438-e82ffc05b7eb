const utils = require('util')
const DbConn = require('../base-datasource')
const { findIndex } = require('lodash')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')
const {
  buildSelectSql,
  wrapAggregate,
  compare,
  formattedDate,
  generateSign
} = require('../../extend/sql-utils')
const { Parser } = require('node-sql-parser')
const moment = require('moment')
const parser = new Parser()

// 优化: 移除了未使用的imports
// 优化: 将常量提取到顶部
const RELATIVE_DATE_FILTERS = [
  'today',
  'lastday', 
  'last7Days',
  'last30Days',
  'last90Days',
  'lastYear'
]

function Gbase8s(config, service) {
  this.config = {
    host: String(config.host) || 'localhost',
    user: String(config.username) || 'root', 
    password: String(config.password) || '',
    port: config.port || 9088, // GBase 8s默认端口
    database: config.database,
    timezone: '08:00',
    schema: config.schema
  }
  this.service = service
  this.config = Object.assign(config, this.config)
}

utils.inherits(Gbase8s, DbConn)

Gbase8s.prototype._request = async function(url, sql) {
  const { host, port, user, password, database } = this.config
  const { ctx } = this.service
  const base = this.service.config.PDBC.address
  // GBase 8s的连接字符串
  const connStr = `jdbc:gbase://${host}:${port}/${database}?rewriteBatchedStatements=true`
  let params = {
    conn_str: connStr,
    driver_str: 'com.gbase.Driver',
    username: user,
    password
  }

  if (sql) {
    params = {
      ...params,
      sql
    }
  }

  try {
    const result = await ctx.curl(base + url, {
      headers: {
        'Content-Type': 'application/json'
      },
      dataType: 'json',
      data: params,
      method: 'POST',
      timeout: 5 * 60 * 1000
    })

    if (result.data.status === 'success') {
      return result.data.result
    }
    throw new SeatomException(+result.code, result.data.reason)
  } catch (error) {
    ctx.logger.info('jdbc接口报错', error)
    throw new SeatomException(400, error)
  }
}

// 优化: 将数据处理逻辑抽象为独立函数
function handleGetData(data) {
  if (data.schema.length > 0 && data.data.length > 0) {
    return data.data.map(item => {
      const obj = {}
      data.schema.forEach((it, index) => {
        obj[it.name] = item[index]
      })
      return obj
    })
  }
  return []
}

Gbase8s.prototype.getFieldList = async function(config) {
  const dateFields = []
  const stringFields = []
  const numberFields = []
  if (this.service.config.PDBC && this.service.config.PDBC.gbase8sEnable) {
    const url = `/sqltree/columns?catalog=${config.database}&schema=${this.config.schema || ''}&table=${config.tbName}`
    const result = await this._request(url)
    
    Object.keys(result).forEach(key => {
      const field = {
        name: result[key].name,
        data_type: result[key].raw_type
      }
      
      // GBase 8s的数据类型映射
      switch(result[key].type.toLowerCase()) {
        case 'char':
        case 'varchar':
        case 'lvar':
        case 'text':
        case 'string':
          stringFields.push(field)
          break
        case 'decimal':
        case 'money':
        case 'integer':
        case 'smallint':
        case 'float':
        case 'double':
        case 'bigint':
        case 'number':
          numberFields.push(field)
          break
        case 'date':
        case 'datetime':
        case 'timestamp':
          dateFields.push(field)
          break
      }
    })
  }

  return [
    { type: 'date', fields: dateFields },
    { type: 'string', fields: stringFields },
    { type: 'number', fields: numberFields }
  ]
}

Gbase8s.prototype.getTreeList = async function() {
  if (this.service.config.PDBC && this.service.config.PDBC.gbase8sEnable) {
    const url = `/sqltree/tables?catalog=${this.config.database}&schema=${this.config.schema || ''}`
    const res = await this._request(url)
    console.log('res', res)
    return res.map(item => ({ TABLE_NAME: item }))
  }
  return []
}

Gbase8s.prototype.dblist = async function() {
  if (this.service.config.PDBC && this.service.config.PDBC.gbase8sEnable) {
    const url = '/sqltree/catalogs'
    const res = await this._request(url)
    return res.map(item => ({ schema_name: item }))
  }
  return []
}

function processCompareValue(item, dateBaseType, isLastItem, composeOperator) {
  let whereClause = compare[item.type][item.compare]

  let value = item.compareValue[0]
  if (item.type === 'string' || item.type === 'date') {
    value = `'${value}'`
  }

  whereClause = whereClause.replace('{0}', value)

  return whereClause + (isLastItem ? ' ' : composeOperator)
}

function processTwoCompareValues(item, isLastItem, composeOperator) {
  let whereClause = compare[item.type][item.compare]

  let value1 = item.compareValue[0]
  let value2 = item.compareValue[1]

  if (item.type === 'string' || item.type === 'date') {
    value1 = `'${value1}'`
    value2 = `'${value2}'`
  }

  whereClause = whereClause.replace('{0}', value1).replace('{1}', value2)

  return whereClause + (isLastItem ? ' ' : composeOperator)
}

function processOrderCondition(orderCondition) {
  return orderCondition.map(item => `"${item.field}" ${item.orderBy || 'ASC'}`)
}

Gbase8s.prototype.processWhereCondition = function(whereCondition, dateBaseType = 'gbase8s') {
  let whereSql = ''

  whereCondition.forEach((item, index) => {
    if (RELATIVE_DATE_FILTERS.includes(item.compare)) {
      item.compareValue = this.relativeDate(item)
    }

    const isFirstItem = index === 0
    const isLastItem = whereCondition.length === index + 1
    const composeOperator = item.composeType === 'or' ? ' ) OR ' : ' ) AND '

    // GBase 8s使用双引号引用字段名
    if (item.compare === 'unequal' && dateBaseType === 'gbase8s' && item.type === 'date') {
      whereSql += isFirstItem
        ? ` ${generateSign(whereCondition.length)} NVL("${item.field}",' ') `
        : `NVL("${item.field}",' ') `
    } else if (item.compare === 'contained') {
      whereSql += isFirstItem
        ? ` ${generateSign(whereCondition.length)} '${item.compareValue[0]}'`
        : `'${item.compareValue[0]}'`
    } else {
      whereSql += isFirstItem
        ? ` ${generateSign(whereCondition.length)} "${item.field}" `
        : `"${item.field}" `
    }

    // 处理比较值
    switch (item.compareValue.length) {
      case 0:
        whereSql += compare[item.type][item.compare] + (isLastItem ? ' ' : composeOperator)
        break
      case 1:
        whereSql += processCompareValue(item, dateBaseType, isLastItem, composeOperator)
        break
      case 2:
        whereSql += processTwoCompareValues(item, isLastItem, composeOperator)
        break
      default:
        if (item.compareValue.length > 2) {
          const inItem = JSON.stringify(item.compareValue).replace(/\[|]/g, '')
          whereSql += compare[item.type][item.compare].replace('{0}', inItem) +
            (isLastItem ? ' ' : composeOperator)
        }
    }
  })

  return whereSql
}

Gbase8s.prototype.hanleData = async function(baseConfig, componentConfig, queryParams) {
  const selectList = []
  const groupByList = []
  let orderByList = []
  let whereList = ''
  
  if (!queryParams.workspaceId && queryParams.workspaceId !== 0) {
    throw new SeatomException(ERROR_CODES.PARAMETER_IS_REQUIRED, '缺失workspaceId参数')
  }

  // 处理维度字段，使用双引号
  componentConfig.fields.dimension.forEach(element => {
    if (element.name) {
      if (element.original_name) {
        if (element.name === element.original_name) {
          selectList.push('"' + element.original_name + '"')
        } else {
          selectList.push('"' + element.original_name + '" AS "' + element.name + '"')
        }
        groupByList.push('"' + element.original_name + '"')
      } else {
        selectList.push('"' + element.name + '" AS "' + element.name + '"')
        groupByList.push('"' + element.name + '"')
      }
    }
  })

  // 处理数值字段
  let hasNumField = false
  const numFile = []
  const datefile = []
  
  componentConfig.fields.numericalValue.forEach(element => {
    if (element.name && element.calculation) {
      hasNumField = true
      const fieldStr = wrapAggregate(
        element.calculation,
        element.original_name ? '"' + element.original_name + '"' : '"' + element.name + '"',
        'gbase8s',
        element.data_type,
        element.name + '(' + element.calculation + ')'
      )
      numFile.push({
        name: element.name + '(' + element.calculation + ')',
        formatter: element.formatter
      })
      selectList.push(fieldStr)
    }
  })

  if (selectList.length === 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  }

  // 处理where条件
  if (componentConfig.where.enable) {
    console.log(componentConfig.where.whereCondition, 'componentConfig.where.whereCondition')
    whereList = this.processWhereCondition(componentConfig.where.whereCondition)
    
    // 处理排序
    if (componentConfig.where.orderCondition && componentConfig.where.orderCondition.length) {
      console.log('componentConfig.where.orderCondition', componentConfig.where.orderCondition)
      orderByList = processOrderCondition(componentConfig.where.orderCondition)
    }
  }

  const limitNum = componentConfig.isLimit ? componentConfig.limitNum : 500
  const tbName = this.config.schema ? `"${this.config.schema}"."${componentConfig.tbName}"` : `"${componentConfig.tbName}"`
  const sql = buildSelectSql(
    tbName,
    selectList,
    whereList,
    groupByList,
    orderByList,
    limitNum,
    'gbase8s',
    hasNumField
  )
  console.log(sql, 'sql')

  return { sql, numFile, datefile }
}

Gbase8s.prototype.getData = async function(baseConfig, componentConfig, queryParams) {
  let sql
  let numFile = []
  let datefile = []

  if (componentConfig.advanced) {
    sql = queryParams.params.targetSql || componentConfig.sql
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (const node of ast) {
          if (node.type !== 'select') throw new Error()
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      throw new SeatomException(ERROR_CODES.PARAMETER_IS_REQUIRED, 'sql格式错误')
    }

    const screenSQL = hanleScreen(baseConfig, componentConfig, queryParams, 'gbase8s')
    sql = joinWhere(sql, screenSQL.whereList)
  } else {
    const obj = await this.hanleData(baseConfig, componentConfig, queryParams)
    sql = obj.sql
    numFile = obj.numFile
    datefile = obj.datefile
  }

  let result
  try {
    result = await this.query(sql)
  } catch (error) {
    throw new SeatomException(500, error)
  }

  // 格式化结果
  return formatQueryResult(result, datefile, numFile)
}

Gbase8s.prototype.query = async function(sql) {
  if (!sql || typeof sql !== 'string') {
    throw new Error('sql statement format is illegal')
  }

  sql = sql.trim()
  if (!sql.toLowerCase().startsWith('select')) {
    throw new Error("query sql must start with 'select'")
  }

  this.service.ctx.logger.info('执行SQL:', sql)

  if (this.service.config.PDBC && this.service.config.PDBC.gbase8sEnable) {
    const shortUrl = '/sqltree/query'
    return handleGetData(await this._request(shortUrl, sql))
  }
  
  throw new Error('PDBC is not enabled')
}

// 优化: 将结果格式化逻辑抽象为独立函数
function formatQueryResult(result, datefile, numFile) {
  return result.map(item => {
    const formattedItem = { ...item }
    
    // 格式化日期字段
    datefile.forEach(dateItem => {
      if (formattedItem[dateItem.name] && dateItem.dateFormatter) {
        formattedItem[dateItem.name] = formattedDate(
          formattedItem[dateItem.name],
          dateItem.dateFormatter
        )
      }
    })

    // 格式化数值字段
    numFile.forEach(numItem => {
      if (
        formattedItem[numItem.name] &&
        numItem.formatter &&
        numItem.formatter.num &&
        numItem.formatter.num.digit &&
        numItem.formatter.num.unit
      ) {
        formattedItem[numItem.name] = (
          formattedItem[numItem.name] / numItem.formatter.num.unit
        ).toFixed(numItem.formatter.num.digit)
      }
    })

    return formattedItem
  })
}

module.exports = Gbase8s 
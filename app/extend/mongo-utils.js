const moment = require('moment')
const dateformat = require('../utils/dateformat')

const sqlANdMongoList = [
  { sql: 'equal', mongo: 'eq' },
  { sql: 'unequal', mongo: 'ne' },
  { sql: 'greater', mongo: 'gt' },
  { sql: 'less', mongo: 'lt' },
  { sql: 'greaterOrEqual', mongo: 'gte' },
  { sql: 'lessOrEqual', mongo: 'lte' },
  { sql: 'in', mongo: 'in' }, // 特殊
  { sql: 'notNull', mongo: 'ne' }, // 特殊
  { sql: 'null', mongo: 'eq' }, //特殊
  { sql: 'contain', mongo: 'regex' }, // 特殊 字符串边上要加//
  { sql: 'contained', mongo: 'regex' }, // 同上 而且调换位置 需要单独处理
  { sql: 'notContain', mongo: 'not' }, // 同上上
  { sql: 'matchOnStart', mongo: 'regex' }, // /^ + /
  { sql: 'matchOnEnd', mongo: 'regex' } // / + $/
]
const relative = [
  'today',
  'lastday',
  'last7Days',
  'last30Days',
  'last90Days',
  'lastYear'
]

// 相对时间
function relativeDate(item) {
  let startTime, endTime
  switch (item.compare) {
    case 'today':
      startTime = moment().startOf('days').format('YYYY-MM-DD HH:mm:ss')
      endTime = moment().endOf('days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'lastday':
      startTime = moment()
        .subtract(1, 'days')
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      endTime = moment()
        .subtract(1, 'days')
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      break
    case 'last7Days':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      endTime = moment().format('YYYY-MM-DD HH:mm:ss')
      break
    case 'last30Days':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      endTime = moment().format('YYYY-MM-DD HH:mm:ss')
      break
    case 'last90Days':
      startTime = moment().subtract(90, 'days').format('YYYY-MM-DD HH:mm:ss')
      endTime = moment().format('YYYY-MM-DD HH:mm:ss')
      break
    case 'lastYear':
      startTime = moment().subtract(1, 'years').format('YYYY-MM-DD HH:mm:ss')
      endTime = moment().format('YYYY-MM-DD HH:mm:ss')
      break
  }
  return [startTime, endTime]
}

function wrapMongoAggregate(type, calculation, originalName) {
  let res = {}
  if (calculation == 'count') {
    res[`$count`] = {}
  } else if (calculation == 'count_distinct') {
    res[`$addToSet`] = `$sheetData.${originalName}`
  } else {
    res[`$${calculation}`] = `$sheetData.${originalName}`
  }
  return res
}

function mongoProjectAggregate(type, calculation, newName) {
  let res = {}
  if (calculation == 'count_distinct') {
    res.$size = `$${newName}`
  } else {
    res = `$${newName}`
  }
  return res
}

// 拼一个condition出来
function generateCondition(condition) {
  const targetPipe = {}
  console.log('condition', condition)
  // 如果是字符串类型
  if (condition.type === 'string') {
    // 找到和compare 对应的mongo符号
    const mongoSymbol = (
      sqlANdMongoList.find(item => item.sql == condition.compare) || {}
    ).mongo
    switch (condition.compare) {
      case 'contain':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = new RegExp(
          condition.compareValue[0]
        )
        break
      // 被包含如何实现 这是一个问题
      case 'contained':
        // 不会写了
        break
      case 'notContain':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = new RegExp(
          condition.compareValue[0]
        )
        break
      case 'in':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = condition.compareValue
        break
      case 'matchOnStart':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = new RegExp(
          '^' + condition.compareValue[0]
        )
        break
      case 'matchOnEnd':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = new RegExp(
          condition.compareValue[0] + '$'
        )
        break
      case 'null':
        targetPipe[condition.field] = ''
        break
      case 'notNull':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = ''
        break
      default:
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] =
          condition.compareValue[0]
    }
  } else if (condition.type === 'number') {
    // 找到和compare 对应的mongo符号
    const mongoSymbol = (
      sqlANdMongoList.find(item => item.sql == condition.compare) || {}
    ).mongo
    // 数字类型
    switch (condition.compare) {
      case 'in':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = condition.compareValue
        break
      case 'notNull':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = null
        break
      case 'null':
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = null
        break
      case 'range':
        console.log('range===')
        targetPipe[condition.field] = {
          $gte: parseFloat(condition.compareValue[0]),
          $lte: parseFloat(condition.compareValue[1])
        }
        break
      default:
        console.log('default===')
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = parseFloat(
          condition.compareValue[0]
        )
        break
    }
  } else {
    // 日期类型
    if (relative.includes(condition.compare)) {
      condition.compareValue = relativeDate(condition)
    }
    if (
      condition.compareValue.length == 0 ||
      condition.compareValue.length == 1
    ) {
      const mongoSymbol = (
        sqlANdMongoList.find(item => item.sql == condition.compare) || {}
      ).mongo
      // 只有一个compareValue的
      if (condition.compare == 'null' || condition.compare == 'notNull') {
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = null
      } else {
        targetPipe[condition.field] = targetPipe[condition.field] || {}
        targetPipe[condition.field][`$${mongoSymbol}`] = dateformat.format(
          new Date(condition.compareValue[0]),
          'yyyy-MM-dd'
        )
      }
    } else {
      // 有多个compareValue的，分别处理
      targetPipe[condition.field] = {
        $gte: dateformat.format(
          new Date(condition.compareValue[0]),
          'yyyy-MM-dd'
        ),
        $lte: dateformat.format(
          new Date(condition.compareValue[1]),
          'yyyy-MM-dd'
        )
      }
    }
  }
  return targetPipe
}

// 递归生成目标管道的方法
function buildMatchCondition(condition) {
  if (condition.length == 1) {
    return condition[0].condition
  }
  if (condition.length > 1) {
    let composeType = condition[0].composeType
    let currentCondition = condition[0].condition
    let nextConditions = condition.slice(1)
    const newCondition = {
      [`$${composeType}`]: [currentCondition, nextConditions[0].condition]
    }
    const conditionList = [
      { condition: newCondition, composeType: nextConditions[0].composeType },
      ...nextConditions.slice(1)
    ]
    return buildMatchCondition(conditionList)
  }
}

function mongoMatchAggregate(whereList) {
  const conditions = whereList.map((item, index) => {
    return {
      condition: generateCondition(item),
      composeType: item.composeType
    }
  })
  const matchCondition = buildMatchCondition(conditions)
  const pipeLine = { $match: matchCondition }
  const str = JSON.stringify(pipeLine)
  console.log('pipeLine', str)
  return pipeLine
}

function calulateDateMongo(func, arg) {
  let mongoExpr = {}
  switch (func) {
    case 'caculate_year':
      mongoExpr = { $year: `$${arg}` }
      break
    case 'caculate_quarter':
      mongoExpr = {
        $concat: [
          { $toString: { $year: `$${arg}` } },
          '年',
          '第',
          { $toString: { $ceil: { $divide: [{ $month: `$${arg}` }, 3] } } },
          '季度'
        ]
      }
      break
    case 'caculate_month':
      mongoExpr = { $dateToString: { format: '%Y-%m', date: `$${arg}` } }
      break
    case 'caculate_week':
      mongoExpr = {
        $concat: [
          { $toString: { $isoWeekYear: `$${arg}` } },
          '年',
          '第',
          { $toString: { $isoWeek: `$${arg}` } },
          '周'
        ]
      }
      break
    case 'caculate_day':
      mongoExpr = { $dateToString: { format: '%Y-%m-%d', date: `$${arg}` } }
      break
    case 'caculate_hour':
      mongoExpr = { $dateToString: { format: '%Y-%m-%dT%H', date: `$${arg}` } }
      break
    case 'caculate_min':
      mongoExpr = {
        $dateToString: { format: '%Y-%m-%dT%H:%M', date: `$${arg}` }
      }
      break
    case 'caculate_second':
      mongoExpr = {
        $dateToString: { format: '%Y-%m-%dT%H:%M:%S', date: `$${arg}` }
      }
      break
    default:
      break
  }
  return mongoExpr
}

function buildMongoSort(where) {
  const addFields = {}
  const sortObj = {}

  where.orderCondition.forEach(item => {
    if (item.calculation) {
      const field = calulateDateMongo(item.calculation, item.fid)
      const newFieldName = `${item.calculation}_${item.fid}`
      addFields[newFieldName] = field
      sortObj[newFieldName] = item.orderBy.toLowerCase() === 'desc' ? -1 : 1
    } else {
      const field = item.field
      sortObj[field] = item.orderBy.toLowerCase() === 'desc' ? -1 : 1
    }
  })

  if (Object.keys(addFields).length > 0) {
    return { addFields: addFields, sortObj: sortObj }
  } else {
    return { sortObj: sortObj }
  }
}

module.exports = {
  wrapMongoAggregate,
  mongoProjectAggregate,
  mongoMatchAggregate,
  buildMongoSort
}

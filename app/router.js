'use strict'
const fs = require('fs-extra')
const path = require('path')
/**
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
  const { router, controller, config } = app
  const publicPath = config.publicPath
  const loginMiddleware = app.middleware.loginMiddleware(config)
  app.io.of('/chat').route('chat', app.io.controller.chat.index) // 终端控制

  const editorNsp = app.io.of('/editor')
  editorNsp.route('joinScreen', app.io.controller.editor.joinScreen)
  editorNsp.route('exitScreen', app.io.controller.editor.exitScreen)
  editorNsp.route('joinScenePage', app.io.controller.editor.joinScenePage)
  editorNsp.route('exitScenePage', app.io.controller.editor.exitScenePage)

  app.io.of('/linkage').route('linkage', app.io.controller.nsp.exchange) //大屏联动
  router.get('/', controller.home.index)
  /** ********** 工作空间-start ************/
  router.get(publicPath + '/api/workspace', controller.workspace.index)
  router.post(publicPath + '/api/workspace/create', controller.workspace.create)
  router.post(publicPath + '/api/workspace/delete', controller.workspace.delete)
  router.post(publicPath + '/api/workspace/update', controller.workspace.update)
  /** ********** 工作空间-end ************/

  /** ********** 项目-start ************/
  router.get(publicPath + '/api/project', controller.project.index)
  router.post(publicPath + '/api/project/create', controller.project.create)
  router.post(publicPath + '/api/project/delete', controller.project.delete)
  router.post(publicPath + '/api/project/update', controller.project.update)
  router.get(
    publicPath + '/api/project/getProjects',
    controller.project.getProjects
  )
  /** ********** 项目-end ************/

  /** ********** 大屏-start ************/
  router.get(
    publicPath + '/api/screen',
    loginMiddleware,
    controller.screen.index
  )
  router.get(publicPath + '/api/screen/findChild', controller.screen.findChild) // 获取子大屏数据
  router.post(publicPath + '/api/screen/list', controller.screen.getScreenList) // 获取大屏列表
  router.get(
    publicPath + '/api/screen/info',
    loginMiddleware,
    controller.screen.info
  )
  router.post(publicPath + '/api/screen/create', controller.screen.create)
  router.post(publicPath + '/api/screen/delete', controller.screen.delete)
  router.post(publicPath + '/api/screen/update', controller.screen.update)
  router.post(publicPath + '/api/screen/coms', controller.screen.screenComs) //获取当前大屏组件
  router.get(publicPath + '/api/screen/export', controller.screen.export)
  router.get(
    publicPath + '/api/screen/batchExport',
    controller.screen.batchExport
  )
  router.post(publicPath + '/api/screen/import', controller.screen.import)
  router.post(publicPath + '/api/screen/copy', controller.screen.copy)
  router.post(publicPath + '/api/screen/addPage', controller.screen.addPage) // 场景大屏添加页面
  router.post(publicPath + '/api/screen/addScene', controller.screen.addScene) // 场景大屏添加场景
  router.post(
    publicPath + '/api/screen/deletePage',
    controller.screen.deletePage
  )
  router.post(
    publicPath + '/api/screen/deleteScene',
    controller.screen.deleteScene
  )
  router.post(
    publicPath + '/api/screen/updateName',
    controller.screen.updateName
  ) // 更新大屏名字
  router.post(publicPath + '/api/screen/move', controller.screen.move)
  router.post(
    publicPath + '/api/screen/orderScene',
    controller.screen.orderScene
  ) // 大屏场景排序
  router.post(publicPath + '/api/screen/orderPage', controller.screen.orderPage) //大屏页面排序
  router.post(publicPath + '/api/screen/importTpl', controller.screen.importTpl) // 导出大屏模版
  router.post(
    publicPath + '/api/screen/getScreensComs',
    controller.screen.getScreensComs
  ) // 获取多个大屏组件
  router.post(
    publicPath + '/api/screen/updateParentId',
    controller.screen.updateParentId
  ) // 更新父屏id
  router.get(
    publicPath + '/api/screen/getscreenlinkAge',
    controller.screen.getscreenlinkAge
  ) // 获取大屏联动信息
  /** ********** 大屏-end ************/

  /** ********** 大屏图层-start ************/
  router.get(
    publicPath + '/api/screenlayer/layers',
    controller.screenlayer.screenLayers
  )
  router.post(
    publicPath + '/api/screenlayer/deleteLayerByIds',
    controller.screenlayer.deleteLayerByIds
  )
  router.post(
    publicPath + '/api/screenlayer/moveLayerByIds',
    controller.screenlayer.moveLayerByIds
  )
  router.post(
    publicPath + '/api/screenlayer/dragInsertLayer',
    controller.screenlayer.dragInsertLayer
  )
  router.post(
    publicPath + '/api/screenlayer/insertLayers',
    controller.screenlayer.insertLayers
  )
  router.post(
    publicPath + '/api/screenlayer/updateSelectedLayers',
    controller.screenlayer.updateSelectedLayers
  )
  router.post(
    publicPath + '/api/screenlayer/createLayerGroups',
    controller.screenlayer.createLayerGroups
  )
  router.post(
    publicPath + '/api/screenlayer/cancelLayerGroups',
    controller.screenlayer.cancelLayerGroups
  )
  /** ********** 大屏图层-end ************/

  /** ********** 大屏发布-start ************/
  router.get(publicPath + '/api/screen/share', controller.screenshare.index)
  router.post(
    publicPath + '/api/screen/share/update',
    controller.screenshare.update
  )
  router.post(
    publicPath + '/api/screen/share/verify',
    controller.screenshare.verify
  )
  router.get(
    publicPath + '/api/screen/clearConfig',
    controller.screen.clearConfig
  )
  /************ 大屏发布-end ************/
  /************ 大屏更新全局变量-start************/
  router.get(
    publicPath + '/api/screen/deletevariableList',
    controller.screen.deletevariableList
  )
  router.get(
    publicPath + '/api/screen/variableList',
    controller.screen.variableList
  )
  router.post(
    publicPath + '/api/screen/addvariableList',
    controller.screen.addvariableList
  )
  router.post(
    publicPath + '/api/screen/updatevariableList',
    controller.screen.updatevariableList
  )
  /************ 大屏更新全局变量-end ************/

  /** ********** 大屏模板-start ************/
  router.get(publicPath + '/api/screentpl', controller.screentpl.index)
  router.post(publicPath + '/api/screentpl/create', controller.screentpl.create)
  router.post(publicPath + '/api/screentpl/delete', controller.screentpl.delete)
  router.post(publicPath + '/api/screentpl/update', controller.screentpl.update)
  /** ********** 大屏模板-end ************/

  /** ********** 组件实例-start ************/
  router.get(publicPath + '/api/component', controller.component.index)
  router.post(publicPath + '/api/component/create', controller.component.create)
  router.post(
    publicPath + '/api/component/createChild',
    controller.component.createChild
  )
  router.post(publicPath + '/api/component/delete', controller.component.delete)
  router.post(
    publicPath + '/api/component/deleteChild',
    controller.component.deleteChild
  )
  router.post(publicPath + '/api/component/update', loginMiddleware, controller.component.update)
  router.post(publicPath + '/api/component/data', controller.component.data)
  router.post(publicPath + '/api/component/copy', controller.component.copy)
  router.post(
    publicPath + '/api/component/copyChild',
    controller.component.copyChild
  )
  router.post(
    publicPath + '/api/component/upgrade',
    controller.component.upgrade
  )
  router.post(publicPath + '/api/component/info', controller.component.info)
  router.post(
    publicPath + '/api/component/cominfo',
    controller.component.cominfo
  )
  router.get(
    publicPath + '/api/component/download',
    controller.component.download
  )
  /** ********** 组件实例-end ************/

  /** ********** 组件包模板-start ************/
  router.get(publicPath + '/api/templates', controller.template.index)
  router.get(
    publicPath + '/api/templates/:name/:version',
    controller.template.file
  )
  router.post(publicPath + '/api/templates/create', controller.template.create)
  router.post(publicPath + '/api/templates/delete', controller.template.delete)
  /** ********** 组件包模板-end ************/

  /** ********** 组件包-start ************/
  router.get(publicPath + '/api/packages', controller.package.index)
  router.get(publicPath + '/api/packages/list', controller.package.list)
  router.get(publicPath + '/api/packages/map', controller.package.map)
  router.get(
    publicPath + '/api/packages/exist/:name/:version',
    controller.package.exist
  )
  router.post(publicPath + '/api/packages/create', controller.package.create)
  router.post(publicPath + '/api/packages/delete', controller.package.delete)
  /** ********** 组件包-end ************/

  /** ********** 过滤器-start ************/
  router.get(publicPath + '/api/filter', controller.filter.index)
  router.get(publicPath + '/api/filter/info', controller.filter.info)
  router.post(publicPath + '/api/filter/create', controller.filter.create)
  router.post(
    publicPath + '/api/filter/createFilter',
    controller.filter.createFilter
  )
  router.post(publicPath + '/api/filter/delete', controller.filter.delete)
  router.post(
    publicPath + '/api/filter/deleteFilter',
    controller.filter.deleteFilter
  )
  router.post(publicPath + '/api/filter/update', controller.filter.update)
  router.post(publicPath + '/api/filter/order', controller.filter.order)
  /** ********** 过滤器-end ************/

  /** ********** 公共方法-start ************/
  router.post(publicPath + '/api/common/upload', controller.common.upload)
  router.post(
    publicPath + '/api/common/uploadLicence',
    controller.common.uploadLicence
  )
  router.post(
    publicPath + '/api/common/uploadfileList',
    controller.common.uploadfileList
  )
  router.post(
    publicPath + '/api/common/customRequest',
    controller.common.customRequest
  )
  router.post(
    publicPath + '/api/common/customApiRequest',
    controller.common.customApiRequest
  )
  router.get(
    publicPath + '/api/common/getSsoUserInfo',
    controller.common.getSsoUserInfo
  )
  router.get(publicPath + '/api/common/licence', controller.common.licence)
  router.get(publicPath + '/api/common/apitest', controller.common.apitest)
  router.get(
    publicPath + '/api/common/getLicenceCode',
    controller.common.getLicenceCode
  ) // 获取授权代码
  router.post(
    publicPath + '/api/common/dataPermission',
    controller.common.dataPermission
  ) // 数据权限
  router.post(
    publicPath + '/api/common/getExpires',
    controller.common.getExpires
  ) // 获取过期日期
  /** ********** 公共方法-end ************/

  /** ********** 组件打包-start ************/
  router.post(publicPath + '/api/packcom/pack', controller.packcom.pack)
  router.post(publicPath + '/api/packcom/index', controller.packcom.index)
  router.post(
    publicPath + '/api/packcom/getcomjson',
    controller.packcom.getcomjson
  ) // 获取组件信息
  router.post(publicPath + '/api/packcom/update', controller.packcom.update)
  /** ********** 组件打包-end ************/

  /** ********** 组件发布-start ************/
  router.post(publicPath + '/api/publishcom', controller.publishcom.publish)
  /** ********** 组件发布-end ************/

  /** ********** 数据源-start ************/
  router.get(
    publicPath + '/api/datastorage/index',
    controller.datastorage.index
  )
  router.get(
    publicPath + '/api/datastorage/list',
    loginMiddleware,
    controller.datastorage.list
  )
  router.post(
    publicPath + '/api/datastorage/create',
    controller.datastorage.create
  )
  router.post(
    publicPath + '/api/datastorage/delete',
    controller.datastorage.delete
  )
  router.post(
    publicPath + '/api/datastorage/update',
    controller.datastorage.update
  )
  router.get(
    publicPath + '/api/datastorage/getDbList',
    controller.datastorage.getDbList
  ) // 获取数据库列表
  router.get(
    publicPath + '/api/datastorage/getData',
    controller.datastorage.getData
  )
  router.post(
    publicPath + '/api/datastorage/getData',
    controller.datastorage.getData
  )
  router.post(
    publicPath + '/api/getData/clearCache',
    controller.datastorage.clearCache
  )
  router.get(
    publicPath + '/api/datastorage/verifySql',
    controller.datastorage.verifySql
  )
  router.post(
    publicPath + '/api/datastorage/getTree',
    controller.datastorage.getTreeList
  )
  router.post(
    publicPath + '/api/datastorage/getTbList',
    controller.datastorage.getFolderTbList
  )
  router.post(
    publicPath + '/api/datastorage/getFieldList',
    controller.datastorage.getFieldList
  )
  router.post(
    publicPath + '/api/datastorage/searchTb',
    controller.datastorage.searchTb
  )
  router.get(
    publicPath + '/api/datastorage/preview',
    controller.datastorage.preview
  )
  router.get(
    publicPath + '/api/datastorage/getFunctionList',
    controller.datastorage.getFunctionList
  ) // 获取函数列表
  router.post(
    publicPath + '/api/datastorage/validateSql',
    controller.datastorage.validateSql
  )
  /** ********** 数据源-end ************/

  /** ********** 对接仪表盘数据源-end ************/
  router.post(
    publicPath + '/api/datadashboard/getProjectTree',
    controller.datadashboard.getProjectTree
  ) // 获取仪表盘列表
  router.post(
    publicPath + '/api/datadashboard/getDashboardInfo',
    controller.datadashboard.getDashboardInfo
  ) // 获取仪表盘详情
  router.post(
    publicPath + '/api/datadashboard/getChartData',
    controller.datadashboard.getChartData
  ) // 获取仪表盘数据
  router.post(
    publicPath + '/api/datadashboard/checkChartData',
    controller.datadashboard.checkChartData
  ) // 校验仪表盘
  /** ********** 对接仪表盘数据源-end ************/

  /** ********** 登陆-start ************/
  router.get(publicPath + '/api/user', controller.user.index)
  router.post(publicPath + '/api/user/dmcLogin', controller.user.dmcLogin)
  router.get(
    publicPath + '/api/user/synchronizeDmcUsers',
    controller.user.synchronizeDmcUsers
  )

  router.post(publicPath + '/api/user/localLogin', controller.user.localLogin) // 本地登录接口
  router.post(publicPath + '/api/user/create', controller.user.create) // 创建用户接口
  router.post(publicPath + '/api/user/getUserId', controller.user.getUserId) // 获取用户id
  router.post(publicPath + '/api/user/register', controller.user.register) // 未用
  router.get(
    publicPath + '/api/user/getAuthorizationDays',
    controller.user.getAuthorizationDays
  ) // 弃用
  router.get(publicPath + '/api/user/checkToken', controller.user.checkToken)
  router.get(
    publicPath + '/api/user/getProductList',
    controller.user.getProductList
  ) // 获取产品列表
  router.get(publicPath + '/api/user/dmcUcenter', controller.user.dmcUcenter) // 获取dmc用户信息
  router.get(
    publicPath + '/api/user/screenConfig',
    controller.user.screenConfig
  ) // 后台配置信息
  router.get(
    publicPath + '/api/user/getDmcUserInfo',
    controller.user.getDmcUserInfo
  )
  router.get(
    publicPath + '/api/user/getUserConfigInfo',
    controller.user.getUserConfigInfo
  )
  router.get(
    publicPath + '/api/user/genRedirecUrl',
    controller.user.genRedirecUrl
  ) // 获取重定向链接
  router.get(
    publicPath + '/api/user/group/category',
    controller.user.getDmcGroupCategory
  )
  router.get(
    publicPath + '/api/group/user/list',
    controller.user.getDmcGroupUserList
  ) // 获取用户分组列表
  router.get(
    publicPath + '/api/user/role/list',
    controller.user.getUserRoleList
  ) // 获取用户角色

  /** ********** 登陆-end ************/

  /** ********** 字体-start ************/
  router.get(publicPath + '/api/font', controller.font.index)
  router.post(publicPath + '/api/font/upload', controller.font.upload)
  router.post(publicPath + '/api/font/update', controller.font.update)
  router.post(publicPath + '/api/font/delete', controller.font.delete)
  /** ********** 字体-end ************/

  /** ********** 截图-start ************/
  router.post(
    publicPath + '/api/screenshot/create',
    controller.screenshot.create
  )
  /** ********** 截图-end ************/

  /** ********** 组件主题-start ************/
  router.get(publicPath + '/api/comtheme', controller.comtheme.index)
  router.post(publicPath + '/api/comtheme/create', controller.comtheme.create)
  router.post(publicPath + '/api/comtheme/update', controller.comtheme.update)
  router.post(publicPath + '/api/comtheme/info', controller.comtheme.info)
  router.post(publicPath + '/api/comtheme/delete', controller.comtheme.delete)
  router.post(publicPath + '/api/comtheme/save', controller.comtheme.save)
  router.post(
    publicPath + '/api/comtheme/saveComtheme',
    controller.comtheme.saveComtheme
  ) // 保存组件主题
  /** ********** 组件主题-end ************/

  /** ********** 地图数据-start ************/
  router.post(publicPath + '/api/mapdata/upload', controller.mapdata.upload)
  /** ********** 地图数据-end ************/

  /** ********** 系统内置过滤器-start ************/
  router.get(publicPath + '/api/systemfilter', controller.systemfilter.index)
  router.post(
    publicPath + '/api/systemfilter/create',
    controller.systemfilter.create
  )
  router.post(
    publicPath + '/api/systemfilter/delete',
    controller.systemfilter.delete
  )
  router.post(
    publicPath + '/api/systemfilter/update',
    controller.systemfilter.update
  )
  router.get(
    publicPath + '/api/systemfilter/export',
    controller.systemfilter.export
  )
  /** ********** 系统内置过滤器-end ************/

  /** ********** 计算字段-start ************/
  router.post(
    publicPath + '/api/calculatefield/create',
    controller.calculatefield.createField
  )
  router.post(
    publicPath + '/api/calculatefield/list',
    controller.calculatefield.getFieldList
  )
  router.post(
    publicPath + '/api/calculatefield/delete',
    controller.calculatefield.deleteField
  )
  /** ********** 计算字段-end ************/

  /** ********** 系统资源接口-start ************/

  /** ********** 计算字段-start ************/
  // router.post(publicPath+'/api/calculatefield/create', controller.calculatefield.createField);
  // router.post(publicPath+'/api/calculatefield/list', controller.calculatefield.getFieldList);
  // router.post(publicPath+'/api/calculatefield/delete', controller.calculatefield.deleteField);
  /** ********** 计算字段-end ************/

  /** ********** 系统资源接口-start ************/
  router.get(publicPath + '/api/resource', controller.resource.index)
  router.post(
    publicPath + '/api/resource/createfolder',
    controller.resource.createfolder
  )
  router.post(publicPath + '/api/resource/upload', controller.resource.upload)
  router.post(publicPath + '/api/resource/delete', controller.resource.delete)
  router.post(
    publicPath + '/api/resource/deletefolder',
    controller.resource.deletefolder
  )
  router.post(
    publicPath + '/api/resource/updateFileName',
    controller.resource.updateFileName
  )
  router.post(
    publicPath + '/api/resource/updatefolder',
    controller.resource.updatefolder
  )
  router.post(publicPath + '/api/resource/move', controller.resource.moveFile)
  router.post(
    publicPath + '/api/resource/uploadScreenIcon',
    controller.resource.uploadScreenIcon
  )
  /** ********** 系统资源接口-end ************/

  /** ********** 主题方案-start ************/
  router.get(publicPath + '/api/themescheme', controller.themescheme.index)
  router.post(
    publicPath + '/api/themescheme/create',
    controller.themescheme.create
  )
  router.post(
    publicPath + '/api/themescheme/getComtheme',
    controller.themescheme.getComtheme
  )
  router.post(
    publicPath + '/api/themescheme/update',
    controller.themescheme.update
  )
  router.post(
    publicPath + '/api/themescheme/delete',
    controller.themescheme.delete
  )
  /** ********** 主题方案-end ************/

  /** ********** 大屏socket-start ************/
  router.get(publicPath + '/api/screensocket', controller.screensocket.index)
  router.post(
    publicPath + '/api/screensocket/update',
    controller.screensocket.update
  )
  router.post(
    publicPath + '/api/screensocket/verify',
    controller.screensocket.verify
  )
  router.post(
    publicPath + '/api/screensocket/connect',
    controller.screensocket.connect
  )

  /** ********** 大屏socket-end ************/

  /** ********** 自定义组件-start ************/
  router.get(
    publicPath + '/api/customcomponent',
    controller.customcomponent.index
  )
  router.post(
    publicPath + '/api/customcomponent/create',
    controller.customcomponent.create
  )
  router.get(
    publicPath + '/api/customcomponent/info',
    controller.customcomponent.info
  )
  router.post(
    publicPath + '/api/customcomponent/update',
    controller.customcomponent.update
  )
  router.post(
    publicPath + '/api/customcomponent/publish',
    controller.customcomponent.publish
  )
  router.post(
    publicPath + '/api/customcomponent/delete',
    controller.customcomponent.delete
  )
  router.post(
    publicPath + '/api/customcomponent/createComScreenshot',
    controller.customcomponent.createComScreenshot
  )
  router.post(
    publicPath + '/api/customcomponent/packCom',
    controller.customcomponent.packCom
  ) // 自定义组件打包
  /** ********** 自定义组件-end ************/

  /** ********** 向指定用户分享大屏-start ************/
  router.post(publicPath + '/api/usershare', controller.usershare.index)
  router.post(publicPath + '/api/usershare/create', controller.usershare.create)
  router.post(publicPath + '/api/usershare/update', controller.usershare.update)
  router.get(
    publicPath + '/api/usershare/userlist',
    controller.usershare.userlist
  )
  router.get(
    publicPath + '/api/usershare/rolelist',
    controller.usershare.rolelist
  )
  router.get(
    publicPath + '/api/usershare/grouplist',
    controller.usershare.grouplist
  )
  router.get(
    publicPath + '/api/usershare/getDmcGroupSublistList',
    controller.usershare.getDmcGroupSublistList
  )
  router.post(publicPath + '/api/usershare/delete', controller.usershare.delete)
  router.get(publicPath + '/api/usershare/list', controller.usershare.list)
  router.post(
    publicPath + '/api/usershare/getListByShareIds',
    controller.usershare.getListByShareIds
  )
  /** ********** 向指定用户分享大屏-end ************/

  /** ********** 自定义组件分组-start ************/
  router.get(
    publicPath + '/api/customcomproject',
    controller.customcomproject.index
  )
  router.post(
    publicPath + '/api/customcomproject/create',
    controller.customcomproject.create
  )
  router.get(
    publicPath + '/api/customcomproject/info',
    controller.customcomproject.info
  )
  router.post(
    publicPath + '/api/customcomproject/update',
    controller.customcomproject.update
  )
  router.post(
    publicPath + '/api/customcomproject/delete',
    controller.customcomproject.delete
  )
  /** ********** 自定义组件分组-end ************/

  /** ********** 消息接口-start ************/
  router.post(publicPath + '/api/message', controller.message.index)
  router.post(publicPath + '/api/message/create', controller.message.create)
  router.post(publicPath + '/api/message/update', controller.message.update)
  router.post(publicPath + '/api/message/delete', controller.message.delete)
  router.post(publicPath + '/api/message/readMsg', controller.message.readMsg)
  /** ********** 消息接口-end ************/

  /** **********  文档教程-start ************/
  router.post(publicPath + '/api/guide', controller.guide.index)
  router.post(publicPath + '/api/guide/upload', controller.guide.upload)
  router.post(publicPath + '/api/guide/deleteFile', controller.guide.deleteFile)
  router.post(
    publicPath + '/api/guide/createContent',
    controller.guide.createContent
  )
  router.post(
    publicPath + '/api/guide/deleteContent',
    controller.guide.deleteContent
  )
  router.post(publicPath + '/api/guide/rename', controller.guide.rename)
  router.post(publicPath + '/api/guide/createDoc', controller.guide.createDoc)
  router.post(publicPath + '/api/guide/updateDoc', controller.guide.updateDoc)
  router.post(publicPath + '/api/guide/deleteDoc', controller.guide.deleteDoc)
  router.get(
    publicPath + '/api/guide/getFrontCover',
    controller.guide.getFrontCover
  )
  router.post(publicPath + '/api/guide/getDocList', controller.guide.getDocList)
  router.post(publicPath + '/api/guide/getMarks', controller.guide.getMarks)
  router.get(publicPath + '/api/guide/getDocById', controller.guide.getDocById)
  router.post(
    publicPath + '/api/guide/changeContent',
    controller.guide.changeContent
  )
  router.post(publicPath + '/api/guide/saveDraft', controller.guide.saveDraft)
  router.post(publicPath + '/api/guide/getDraft', controller.guide.getDraft)
  /** ********** 文档教程-end ************/

  /** 代码自动更新接口**/
  router.post(
    publicPath + '/api/upgrade/upgradeFront',
    controller.upgrade.upgradeFront
  )
  router.post(
    publicPath + '/api/upgrade/upgradeBackEnd',
    controller.upgrade.upgradeBackEnd
  )
  router.post(
    publicPath + '/api/upgrade/upgradeComponent',
    controller.upgrade.upgradeComponent
  )

  /** 图层信息接口**/
  router.post(publicPath + '/api/layergroup', controller.layergroup.index)
  router.post(
    publicPath + '/api/layergroup/create',
    controller.layergroup.create
  )
  router.post(
    publicPath + '/api/layergroup/update',
    controller.layergroup.update
  )

  /** 预览页面缓存接口**/
  router.get(publicPath + '/api/preview/packagesMap', controller.preview.map) // 缓存组件列表
  router.get(publicPath + '/api/preview/screen', controller.preview.screen) // 缓存大屏信息
  router.post(
    publicPath + '/api/preview/getScreensComs',
    controller.preview.getScreensComs
  ) // 获取缓存大屏组件
  router.post(publicPath + '/api/preview/getData', controller.preview.getData) // 获取缓存数据
  router.get(
    publicPath + '/api/preview/clearCache',
    controller.preview.clearCache
  ) // 清除缓存
  router.get(
    publicPath + '/api/preview/updateCache',
    controller.preview.updateCache
  ) // 更新缓存
  /** 第三方接口列表**/
  router.post(publicPath + '/api/open/getToken', controller.openapi.getToken)
  router.post(
    publicPath + '/api/open/update/datastorage/autoUpdate',
    controller.openapi.autoUpdate
  )
  router.post(publicPath + '/api/open/project', controller.openapi.project)
  router.post(publicPath + '/api/open/com/comInfo', controller.openapi.comInfo)
  router.post(
    publicPath + '/api/open/com/getComData',
    controller.openapi.getComData
  )
  router.post(
    publicPath + '/api/open/com/comInfotest',
    controller.openapi.comInfotest
  )
  router.post(
    publicPath + '/api/open/com/comInfoList',
    controller.openapi.comInfoList
  )
  router.post(
    publicPath + '/api/open/com/screenComs',
    controller.openapi.screenComs
  )
  router.post(
    publicPath + '/api/open/user/compList',
    controller.openapi.userComList
  ) // 获取当前用户组件信息
  router.post(
    publicPath + '/api/open/screen/compList',
    controller.openapi.screenComList
  ) // 获取当前大屏下的组件信息
  router.get(
    publicPath + '/api/open/screen/screenLayers',
    controller.openapi.screenLayers
  )
  router.get(
    publicPath + '/api/open/getuserInfo',
    controller.openapi.getuserInfo
  )
  router.post(
    publicPath + '/api/screen/importOverSizeScreen',
    controller.screen.importOverSizeScreen
  )
  /** 第三方接口列表**/
  router.post(publicPath + '/api/test/permission', controller.common.permission) // 测试接口

  /** ********** 向指定用户协同编辑大屏-start ************/
  router.get(
    publicPath + '/api/screencoedit/list',
    controller.screencoedit.list
  )
  router.post(
    publicPath + '/api/screencoedit/save',
    controller.screencoedit.save
  )
  router.post(
    publicPath + '/api/screencoedit/move',
    controller.screencoedit.move
  )
  router.get(
    publicPath + '/api/screencoedit/delete',
    controller.screencoedit.delete
  )
  router.get(
    publicPath + '/api/screencoedit/sharedToMe',
    controller.screencoedit.sharedToMe
  )
  router.get(
    publicPath + '/api/screencoedit/checkJoinScreen',
    controller.screencoedit.checkJoinScreen
  )
  router.get(
    publicPath + '/api/screencoedit/getScreenCoeditRooms',
    controller.screencoedit.getScreenCoeditRooms
  )

  /** ********** 向指定用户协同编辑大屏-end ************/
  /** ********** 语音控制-start ************/
  router.post(publicPath + '/api/voicecontrol', controller.voicecontrol.index)
  router.get(
    publicPath + '/api/voicecontrol/find',
    controller.voicecontrol.find
  )

  router.post(
    publicPath + '/api/voicecontrol/update',
    controller.voicecontrol.update
  )

  router.post(
    publicPath + '/api/voicecontrol/create',
    controller.voicecontrol.create
  )
  router.get(
    publicPath + '/api/voicecontrol/comList',
    controller.voicecontrol.comlist
  ) // 获取控制组件
  router.get(
    publicPath + '/api/voicecontrol/createInitControl',
    controller.voicecontrol.createInitControl
  ) // 初始化配置
  router.get(
    publicPath + '/api/voicecontrol/voiceconfig',
    controller.voicecontrol.voiceconfig
  )
  router.post(
    publicPath + '/api/voicecontrol/speech',
    controller.voicecontrol.speech
  )
  /** ********** 语音控制-start ************/

  /** ********** 组件标签 **********/
  router.get(publicPath + '/api/component/tags', controller.label.index)
  router.post(
    publicPath + '/api/component/tags/create',
    controller.label.create
  )
  router.post(
    publicPath + '/api/component/tags/update',
    controller.label.update
  )
  router.post(
    publicPath + '/api/component/tags/delete',
    controller.label.delete
  )
  router.post(
    publicPath + '/api/component/tags/list',
    controller.label.indexByTag
  )

  /** ********** 组件标签-end **********/
  /** ********** seatom-cli start **********/
  router.post(
    publicPath + '/api/seatom/cli/user/create',
    controller.seatomcliuser.create
  )
  router.post(
    publicPath + '/api/seatom/cli/user/login',
    controller.seatomcliuser.login
  )
  /************ seatom-cli end **********/
  /************ seatom ai start **********/
  router.post(publicPath + '/api/screenai/create', controller.screenai.create)
  /************ seatom ai end **********/
  /************ 指标卡 start **********/
  router.post(
    publicPath + '/api/indicatorcard/create',
    controller.indicatorcard.create
  )
  router.get(
    publicPath + '/api/indicatorcard/index',
    controller.indicatorcard.index
  )
  router.post(
    publicPath + '/api/indicatorcard/delete',
    controller.indicatorcard.delete
  )
  router.post(
    publicPath + '/api/indicatorcard/update',
    controller.indicatorcard.update
  )
  router.get(
    publicPath + '/api/indicatorcard/com',
    controller.indicatorcard.com
  )
  /************ 指标卡 end **********/
  /************ 海致ai对话框 start **********/
  router.post(
    publicPath + '/api/aichat/coms/update',
    controller.aichat.comsUpdate
  )
  /************ 海致ai对话框 end **********/
  /************ 海致用户行为记录 start **********/
  router.post(
    publicPath + '/api/userbehavior/log',
    controller.userBehavior.getOpLog
  )
  /************ 海致用户行为记录 end **********/
  /************ prometheus监控监控 start **********/
  router.get(publicPath + '/api/metrics', controller.prometheus.metrics)
  /************ prometheus监控监控 end **********/
  /************ 对接dmc平台指标卡 start **********/
  router.get(
    publicPath + '/api/indicatordmc/getIndicatorDmc',
    controller.indicatordmc.getIndicatorDmc
  )
  router.get(publicPath + '/api/indicatordmc/com', controller.indicatordmc.com)
  router.post(
    publicPath + '/api/indicatordmc/save',
    controller.indicatordmc.save
  )
  router.post(
    publicPath + '/api/indicatordmc/create',
    controller.indicatordmc.create
  )
  router.post(
    publicPath + '/api/indicatordmc/myIndicatorList',
    controller.indicatordmc.myIndicatorList
  ) // 获取平台所有指标
  router.post(
    publicPath + '/api/indicatordmc/publicIndicatorList',
    controller.indicatordmc.publicIndicatorList
  ) // 弃用
  router.post(
    publicPath + '/api/indicatordmc/delete',
    controller.indicatordmc.delete
  )
  /************ 对接dmc平台指标卡 end **********/
  /************ 伏羲配置中心 start **********/
  router.get(publicPath + '/api/config/info', controller.configinfo.index)
  router.post(
    publicPath + '/api/config/update',
    controller.configinfo.update
  )
  /************ 伏羲配置中心 end **********/
}

const { execSync } = require('child_process');
const fs = require('fs');

let branch;
try {
  branch = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
  if (branch === 'HEAD') {
    // 如果是分离头指针状态，获取最近的分支
    const commitHash = execSync('git rev-parse HEAD').toString().trim();
    const branches = execSync(`git branch -r --contains ${commitHash} | grep -v HEAD | awk '{print $1}'`).toString().trim().split('\n');
    if (branches.length > 0) {
      // 优先选择最近的远程分支
      branch = branches[0].replace('origin/', '');
    } else {
      // 如果没有找到匹配的分支，使用提交哈希
      branch = commitHash;
    }
  }
} catch (error) {
  console.error('Error getting git branch:', error);
  process.exit(1);
}

let commitId, commitTime;
try {
  commitId = execSync('git log -n1 --format=format:"%H"').toString().trim();
  commitTime = execSync('git log -n1 --pretty=format:"%ad" --date=iso').toString().trim().substring(0, 19);
} catch (error) {
  console.error('Error getting git commit info:', error);
  process.exit(1);
}

const info = `伏羲后台seatom-server的git信息\nbranch: ${branch}\ncommitId: ${commitId}\ntime: ${commitTime}`;
  fs.writeFileSync('./app/version.txt', info);
  console.warn('========== Save git info done. ==========');
